"use client"

import React, { createContext, use<PERSON>ontext, useReducer, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { AuthState, AuthAction, User, Profile } from './types'

// Initial state
const initialState: AuthState = {
  user: null,
  profile: null,
  loading: true,
  initialized: false,
}

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
      }
    case 'SET_PROFILE':
      return {
        ...state,
        profile: action.payload,
      }
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      }
    case 'SET_INITIALIZED':
      return {
        ...state,
        initialized: action.payload,
        loading: false,
      }
    case 'SIGN_OUT':
      return {
        ...state,
        user: null,
        profile: null,
        loading: false,
      }
    default:
      return state
  }
}

// Context type
interface AuthContextType {
  state: AuthState
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, fullName: string, phone?: string) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error?: any }>
  updatePassword: (password: string) => Promise<{ error?: any }>
  updateProfile: (data: { fullName: string; phone?: string }) => Promise<{ error?: any }>
  refreshSession: () => Promise<void>
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState)
  const supabase = createClient()

  // Fetch user profile
  const fetchProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (session?.user) {
          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            email_confirmed_at: session.user.email_confirmed_at,
            created_at: session.user.created_at,
            updated_at: session.user.updated_at,
          }
          
          dispatch({ type: 'SET_USER', payload: user })
          
          // Fetch profile
          const profile = await fetchProfile(session.user.id)
          if (profile) {
            dispatch({ type: 'SET_PROFILE', payload: profile })
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        dispatch({ type: 'SET_INITIALIZED', payload: true })
      }
    }

    initializeAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            email_confirmed_at: session.user.email_confirmed_at,
            created_at: session.user.created_at,
            updated_at: session.user.updated_at,
          }
          
          dispatch({ type: 'SET_USER', payload: user })
          
          // Fetch profile
          const profile = await fetchProfile(session.user.id)
          if (profile) {
            dispatch({ type: 'SET_PROFILE', payload: profile })
          }
        } else if (event === 'SIGNED_OUT') {
          dispatch({ type: 'SIGN_OUT' })
        }
        
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  // Sign in
  const signIn = async (email: string, password: string) => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        dispatch({ type: 'SET_LOADING', payload: false })
        return { error }
      }

      return {}
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false })
      return { error }
    }
  }

  // Sign up
  const signUp = async (email: string, password: string, fullName: string, phone?: string) => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone: phone || null,
          },
        },
      })

      if (error) {
        dispatch({ type: 'SET_LOADING', payload: false })
        return { error }
      }

      return {}
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false })
      return { error }
    }
  }

  // Sign out
  const signOut = async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    await supabase.auth.signOut()
    dispatch({ type: 'SIGN_OUT' })
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      return { error }
    } catch (error) {
      return { error }
    }
  }

  // Update password
  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password,
      })

      return { error }
    } catch (error) {
      return { error }
    }
  }

  // Update profile
  const updateProfile = async (data: { fullName: string; phone?: string }) => {
    if (!state.user) {
      return { error: { message: 'User not authenticated' } }
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: data.fullName,
          phone: data.phone || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', state.user.id)

      if (error) {
        return { error }
      }

      // Refresh profile
      const profile = await fetchProfile(state.user.id)
      if (profile) {
        dispatch({ type: 'SET_PROFILE', payload: profile })
      }

      return {}
    } catch (error) {
      return { error }
    }
  }

  // Refresh session
  const refreshSession = async () => {
    await supabase.auth.refreshSession()
  }

  const value: AuthContextType = {
    state,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    refreshSession,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
