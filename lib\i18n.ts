// Import translations for each section
import arCommon from '../locales/ar/common.json'
import arHero from '../locales/ar/hero.json'
import arNavbar from '../locales/ar/navbar.json'
import arFooter from '../locales/ar/footer.json'
import arAuth from '../locales/ar/auth.json'

import enCommon from '../locales/en/common.json'
import enHero from '../locales/en/hero.json'
import enNavbar from '../locales/en/navbar.json'
import enFooter from '../locales/en/footer.json'
import enAuth from '../locales/en/auth.json'

export const locales = ['ar', 'en'] as const
export const defaultLocale = 'ar' as const

export type Locale = (typeof locales)[number]

// Merge all translation files for each locale
const translations = {
  ar: {
    common: arC<PERSON>mon,
    hero: ar<PERSON><PERSON>,
    navbar: ar<PERSON><PERSON><PERSON>,
    footer: ar<PERSON><PERSON>er,
    auth: ar<PERSON><PERSON>,
  },
  en: {
    common: enC<PERSON>mon,
    hero: enHero,
    navbar: enNavbar,
    footer: enFooter,
    auth: enAuth,
  },
}

export function getTranslations(locale: Locale) {
  return translations[locale] || translations[defaultLocale]
}

export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}
