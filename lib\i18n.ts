// Import translations for each section
import arCommon from '../locales/ar/common.json'
import arHero from '../locales/ar/hero.json'
import arNavbar from '../locales/ar/navbar.json'
import arFooter from '../locales/ar/footer.json'

import enCommon from '../locales/en/common.json'
import enHero from '../locales/en/hero.json'
import enNavbar from '../locales/en/navbar.json'
import enFooter from '../locales/en/footer.json'

export const locales = ['ar', 'en'] as const
export const defaultLocale = 'ar' as const

export type Locale = (typeof locales)[number]

// Merge all translation files for each locale
const translations = {
  ar: {
    common: arCommon,
    hero: arHero,
    navbar: arNavbar,
    footer: arFooter,
  },
  en: {
    common: enCommon,
    hero: enHero,
    navbar: enNavbar,
    footer: enFooter,
  },
}

export function getTranslations(locale: Locale) {
  return translations[locale] || translations[defaultLocale]
}

export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}
