// Import translations for each section
import arC<PERSON>mon from '../locales/ar/common.json'
import arHero from '../locales/ar/hero.json'
import arNavbar from '../locales/ar/navbar.json'
import arFooter from '../locales/ar/footer.json'
import arAuth from '../locales/ar/auth.json'
import arCart from '../locales/ar/cart.json'

import enCommon from '../locales/en/common.json'
import enHero from '../locales/en/hero.json'
import enNavbar from '../locales/en/navbar.json'
import enFooter from '../locales/en/footer.json'
import enAuth from '../locales/en/auth.json'
import enCart from '../locales/en/cart.json'

export const locales = ['ar', 'en'] as const
export const defaultLocale = 'ar' as const

export type Locale = (typeof locales)[number]

// Merge all translation files for each locale
const translations = {
  ar: {
    common: ar<PERSON><PERSON><PERSON>,
    hero: ar<PERSON><PERSON>,
    navbar: ar<PERSON><PERSON><PERSON>,
    footer: ar<PERSON>ooter,
    auth: arAuth,
    cart: arCart,
  },
  en: {
    common: enCommon,
    hero: enHero,
    navbar: enNavbar,
    footer: enFooter,
    auth: enAuth,
    cart: enCart,
  },
}

export function getTranslations(locale: Locale) {
  return translations[locale] || translations[defaultLocale]
}

export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}
