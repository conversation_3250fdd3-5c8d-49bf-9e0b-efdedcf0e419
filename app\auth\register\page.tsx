"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Mail, Lock, User, Phone, Star, Award, TrendingUp, DollarSign } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthRedirect, useAuthForm } from '@/lib/auth/hooks'
import { registerSchema, type RegisterFormData } from '@/lib/auth/types'
import AuthLayout from '@/components/auth/AuthLayout'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import AuthSuccess from '@/components/auth/AuthSuccess'
import GuestRoute from '@/components/auth/GuestRoute'
import PasswordStrength from '@/components/auth/PasswordStrength'

export default function RegisterPage() {
  const { signUp } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  useAuthRedirect()

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
    watch,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
    },
  })

  const password = watch('password')

  const onSubmit = async (data: RegisterFormData) => {
    clearErrors()
    setSuccessMessage(null)
    
    await handleSubmit(
      async () => {
        const result = await signUp(data.email, data.password, data.fullName, data.phone)
        return result
      },
      () => {
        setSuccessMessage('تم إنشاء الحساب بنجاح! يرجى تأكيد بريدك الإلكتروني')
        toast.success('تم إنشاء الحساب بنجاح')
      }
    )
  }

  const rightContent = (
    <div className="space-y-8">
      <div>
        <h2 className="text-4xl font-bold mb-4 arabic-heading">
          مرحباً بك في نينجا تمبليتس!
        </h2>
        <p className="text-xl text-white/90 arabic-text leading-relaxed">
          انضم لأفضل منصة لبيع القوالب البرمجية واستمتع بفرص ربح ممتازة
        </p>
      </div>

      <div className="space-y-6">
        <h3 className="text-2xl font-semibold arabic-heading">
          انضم إلى مجتمعنا المتنامي
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">أكثر من 10,000 قالب احترافي</h4>
              <p className="text-white/80 arabic-text">مكتبة ضخمة من القوالب عالية الجودة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">جودة عالية ومعايير صارمة</h4>
              <p className="text-white/80 arabic-text">جميع القوالب تخضع لمراجعة دقيقة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">مجتمع من المطورين المحترفين</h4>
              <p className="text-white/80 arabic-text">تواصل مع أفضل المطورين في المنطقة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">فرص ربح ممتازة للمطورين</h4>
              <p className="text-white/80 arabic-text">احصل على عمولات مجزية من مبيعاتك</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <GuestRoute>
      <AuthLayout
        title="إنشاء حساب جديد"
        subtitle="انضم لأفضل منصة لبيع القوالب البرمجية"
        rightContent={rightContent}
      >
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          <AuthError error={errors.general} onDismiss={clearErrors} />
          <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

          <AuthInput
            {...register('fullName')}
            label="الاسم الكامل"
            type="text"
            icon={<User className="w-5 h-5" />}
            error={formErrors.fullName?.message}
            placeholder="أدخل اسمك الكامل"
            required
          />

          <AuthInput
            {...register('email')}
            label="البريد الإلكتروني"
            type="email"
            icon={<Mail className="w-5 h-5" />}
            error={formErrors.email?.message}
            placeholder="أدخل بريدك الإلكتروني"
            required
          />

          <AuthInput
            {...register('phone')}
            label="رقم الهاتف"
            type="tel"
            icon={<Phone className="w-5 h-5" />}
            error={formErrors.phone?.message}
            placeholder="أدخل رقم هاتفك (اختياري)"
            description="رقم الهاتف اختياري ولكن يساعد في التواصل"
          />

          <AuthInput
            {...register('password')}
            label="كلمة المرور"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.password?.message}
            placeholder="أدخل كلمة مرور قوية"
            showPasswordToggle
            required
          />

          {password && (
            <PasswordStrength password={password} />
          )}

          <AuthInput
            {...register('confirmPassword')}
            label="تأكيد كلمة المرور"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.confirmPassword?.message}
            placeholder="أعد إدخال كلمة المرور"
            showPasswordToggle
            required
          />

          <AuthButton
            type="submit"
            loading={isSubmitting}
            loadingText="جاري إنشاء الحساب..."
          >
            إنشاء حساب جديد
          </AuthButton>

          <div className="text-center">
            <p className="text-gray-600 arabic-text">
              لديك حساب بالفعل؟{' '}
              <Link
                href="/auth/login"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </form>
      </AuthLayout>
    </GuestRoute>
  )
}
