"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  Newspaper,
  HelpCircle,
  Headphones,
  Smartphone,
  Palette,
  Grid3X3,
  Globe
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DropdownItem, SubDropdownItem } from '@/lib/site-config'

// Icon mapping for navigation items
const iconMap = {
  'shopping-bag': ShoppingBag,
  'newspaper': Newspaper,
  'help-circle': HelpCircle,
  'headphones': Headphones,
  'smartphone': Smartphone,
  'palette': Palette,
  'grid-3x3': Grid3X3,
  'globe': Globe,
}

interface NestedDropdownMenuProps {
  triggerText: string
  triggerIcon?: string
  items: DropdownItem[]
  className?: string
}

interface CollapsibleSectionProps {
  item: DropdownItem
  isExpanded: boolean
  onToggle: () => void
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({ item, isExpanded, onToggle }) => {
  const IconComponent = item.icon ? iconMap[item.icon as keyof typeof iconMap] : null
  const ChevronIcon = isExpanded ? ChevronDown : (document.documentElement.dir === 'rtl' ? ChevronLeft : ChevronRight)

  return (
    <div className="w-full">
      {/* Main Category Header */}
      <div className="flex items-center justify-between p-4 hover:bg-green-50 transition-colors duration-200">
        <Link
          href={item.href}
          className="flex items-start space-x-3 rtl:space-x-reverse flex-1"
        >
          {IconComponent && (
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mt-0.5">
              <IconComponent className="w-5 h-5 text-green-600" />
            </div>
          )}
          <div className="flex flex-col flex-1">
            <span className="font-semibold text-gray-900 text-base">{item.name}</span>
            {item.description && (
              <span className="text-sm text-gray-500 mt-1 leading-relaxed">
                {item.description}
              </span>
            )}
          </div>
        </Link>
        
        {item.hasSubItems && item.subItems && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="p-2 hover:bg-green-100 transition-colors duration-200"
          >
            <ChevronIcon className="w-4 h-4 text-gray-600" />
          </Button>
        )}
      </div>

      {/* Collapsible Subitems */}
      {item.hasSubItems && item.subItems && isExpanded && (
        <div className="bg-gray-50 border-t border-gray-100">
          <div className="py-2">
            {item.subItems.map((subItem) => {
              const SubIconComponent = subItem.icon ? iconMap[subItem.icon as keyof typeof iconMap] : null
              return (
                <Link
                  key={subItem.href}
                  href={subItem.href}
                  className="flex items-start space-x-3 rtl:space-x-reverse p-3 mx-4 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200"
                >
                  {SubIconComponent && (
                    <div className="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5">
                      <SubIconComponent className="w-4 h-4 text-green-600" />
                    </div>
                  )}
                  <div className="flex flex-col flex-1">
                    <span className="font-medium text-gray-800 text-sm">{subItem.name}</span>
                    {subItem.description && (
                      <span className="text-xs text-gray-500 mt-0.5 leading-relaxed">
                        {subItem.description}
                      </span>
                    )}
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default function NestedDropdownMenu({ 
  triggerText, 
  triggerIcon, 
  items, 
  className = '' 
}: NestedDropdownMenuProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  
  const TriggerIconComponent = triggerIcon ? iconMap[triggerIcon as keyof typeof iconMap] : null

  const toggleExpanded = (itemHref: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemHref)) {
      newExpanded.delete(itemHref)
    } else {
      newExpanded.add(itemHref)
    }
    setExpandedItems(newExpanded)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200 ${className}`}
        >
          {TriggerIconComponent && <TriggerIconComponent className="w-4 h-4" />}
          <span className="arabic-text">{triggerText}</span>
          <ChevronDown className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-96 p-0 max-h-[80vh] overflow-y-auto">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold text-lg text-gray-900 arabic-text">{triggerText}</h3>
            <Link 
              href="/templates"
              className="text-sm text-green-600 hover:text-green-700 font-medium transition-colors duration-200"
            >
              عرض الكل
            </Link>
          </div>
        </div>
        
        <div className="divide-y divide-gray-100">
          {items.map((item) => (
            <CollapsibleSection
              key={item.href}
              item={item}
              isExpanded={expandedItems.has(item.href)}
              onToggle={() => toggleExpanded(item.href)}
            />
          ))}
        </div>
        
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <Link 
            href="/templates"
            className="flex items-center justify-center w-full py-2 text-sm text-green-600 hover:text-green-700 font-medium transition-colors duration-200"
          >
            استعراض جميع القوالب
          </Link>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
