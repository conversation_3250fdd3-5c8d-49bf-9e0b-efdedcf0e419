"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  Newspaper,
  HelpCircle,
  Headphones,
  Smartphone,
  Palette,
  Grid3X3,
  Globe
} from 'lucide-react'
import { UnifiedButton } from '@/components/ui/UnifiedButton'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DropdownItem, SubDropdownItem } from '@/lib/site-config'

// Icon mapping for navigation items
const iconMap = {
  'shopping-bag': ShoppingBag,
  'newspaper': Newspaper,
  'help-circle': HelpCircle,
  'headphones': Headphones,
  'smartphone': Smartphone,
  'palette': Palette,
  'grid-3x3': Grid3X3,
  'globe': Globe,
}

interface NestedDropdownMenuProps {
  triggerText: string
  triggerIcon?: string
  items: DropdownItem[]
  className?: string
}

interface CollapsibleSectionProps {
  item: DropdownItem
  isExpanded: boolean
  onToggle: () => void
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({ item, isExpanded, onToggle }) => {
  const [isRTL, setIsRTL] = useState(false)

  useEffect(() => {
    setIsRTL(document.documentElement.dir === 'rtl')
  }, [])

  const IconComponent = item.icon ? iconMap[item.icon as keyof typeof iconMap] : null
  const ChevronIcon = isExpanded ? ChevronDown : (isRTL ? ChevronLeft : ChevronRight)

  return (
    <div className="w-full">
      {/* Main Category Header */}
      <div className="flex items-center justify-between p-4 hover:bg-green-50 transition-colors duration-200">
        <Link
          href={item.href}
          className="flex items-start space-x-3 rtl:space-x-reverse flex-1"
        >
          {IconComponent && (
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mt-0.5">
              <IconComponent className="w-5 h-5 text-green-600" />
            </div>
          )}
          <div className="flex flex-col flex-1">
            <span className="font-semibold text-gray-900 text-base">{item.name}</span>
            {item.description && (
              <span className="text-sm text-gray-500 mt-1 leading-relaxed">
                {item.description}
              </span>
            )}
          </div>
        </Link>
        
        {item.hasSubItems && item.subItems && (
          <UnifiedButton
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="p-2 hover:bg-green-100 transition-colors duration-200"
          >
            <ChevronIcon className="w-4 h-4 text-gray-600" />
          </UnifiedButton>
        )}
      </div>

      {/* Collapsible Subitems with Animation */}
      <AnimatePresence>
        {item.hasSubItems && item.subItems && isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0.0, 0.2, 1] // Custom easing curve
            }}
            className="bg-gray-50 border-t border-gray-100 overflow-hidden"
          >
            <motion.div
              initial={{ y: -10 }}
              animate={{ y: 0 }}
              exit={{ y: -10 }}
              transition={{
                duration: 0.2,
                delay: 0.1,
                ease: [0.4, 0.0, 0.2, 1]
              }}
              className="py-2"
            >
              {item.subItems.map((subItem, index) => {
                const SubIconComponent = subItem.icon ? iconMap[subItem.icon as keyof typeof iconMap] : null
                return (
                  <motion.div
                    key={subItem.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.2,
                      delay: index * 0.05,
                      ease: [0.4, 0.0, 0.2, 1]
                    }}
                  >
                    <Link
                      href={subItem.href}
                      className="flex items-start space-x-3 rtl:space-x-reverse p-3 mx-4 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200"
                    >
                      {SubIconComponent && (
                        <div className="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5">
                          <SubIconComponent className="w-4 h-4 text-green-600" />
                        </div>
                      )}
                      <div className="flex flex-col flex-1">
                        <span className="font-medium text-gray-800 text-sm">{subItem.name}</span>
                        {subItem.description && (
                          <span className="text-xs text-gray-500 mt-0.5 leading-relaxed">
                            {subItem.description}
                          </span>
                        )}
                      </div>
                    </Link>
                  </motion.div>
                )
              })}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default function NestedDropdownMenu({ 
  triggerText, 
  triggerIcon, 
  items, 
  className = '' 
}: NestedDropdownMenuProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  
  const TriggerIconComponent = triggerIcon ? iconMap[triggerIcon as keyof typeof iconMap] : null

  const toggleExpanded = (itemHref: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemHref)) {
      newExpanded.delete(itemHref)
    } else {
      newExpanded.add(itemHref)
    }
    setExpandedItems(newExpanded)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={`flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200 px-3 py-2 rounded-lg ${className}`}>
          {TriggerIconComponent && <TriggerIconComponent className="w-4 h-4" />}
          <span className="arabic-text">{triggerText}</span>
          <ChevronDown className="w-4 h-4" />
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-[800px] p-0 max-h-[80vh] overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -10 }}
          transition={{
            duration: 0.2,
            ease: [0.4, 0.0, 0.2, 1]
          }}
          className="p-4"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold text-lg text-gray-900 arabic-text">{triggerText}</h3>
            <Link
              href="/templates"
              className="text-sm text-green-600 hover:text-green-700 font-medium transition-colors duration-200"
            >
              عرض الكل
            </Link>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-gray-100">
          {items.map((item, index) => (
            <div key={item.href} className={`${index % 2 === 1 ? 'md:border-t-0' : ''}`}>
              <CollapsibleSection
                item={item}
                isExpanded={expandedItems.has(item.href)}
                onToggle={() => toggleExpanded(item.href)}
              />
            </div>
          ))}
        </div>
        
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <Link 
            href="/templates"
            className="flex items-center justify-center w-full py-2 text-sm text-green-600 hover:text-green-700 font-medium transition-colors duration-200"
          >
            استعراض جميع القوالب
          </Link>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
