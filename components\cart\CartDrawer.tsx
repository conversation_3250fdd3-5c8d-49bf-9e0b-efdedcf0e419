"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, ShoppingBag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/stores/cartStore'
import { useI18n } from '@/contexts/I18nContext'
import { useSession } from '@/lib/auth/hooks'
import cartTranslations from '@/lib/translations/cart.json'
import CartItem from './CartItem'
import EmptyCart from './EmptyCart'
import { toast } from 'sonner'

export default function CartDrawer() {
  const { locale } = useI18n()
  const t = cartTranslations[locale as keyof typeof cartTranslations]
  const { isAuthenticated } = useSession()
  const { 
    items, 
    isOpen, 
    closeCart, 
    getTotalItems, 
    getTotalPrice,
    clearCart 
  } = useCartStore()

  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()

  const formatPrice = (price: number) => {
    return locale === 'ar' ? `${price} ${t.currency}` : `${t.currency} ${price}`
  }

  const handleCheckout = () => {
    if (!isAuthenticated) {
      toast.error(t.loginRequired)
      return
    }
    
    // TODO: Implement checkout logic
    toast.success('Checkout functionality coming soon!')
  }

  const handleClearCart = () => {
    clearCart()
    toast.success(locale === 'ar' ? 'تم إفراغ السلة' : 'Cart cleared')
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/50 z-50"
            onClick={closeCart}
          />

          {/* Drawer */}
          <motion.div
            initial={{ x: locale === 'ar' ? -400 : 400 }}
            animate={{ x: 0 }}
            exit={{ x: locale === 'ar' ? -400 : 400 }}
            transition={{ 
              type: "spring", 
              damping: 30, 
              stiffness: 300,
              duration: 0.3 
            }}
            className={`fixed top-0 ${locale === 'ar' ? 'left-0' : 'right-0'} h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col`}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <ShoppingBag className="w-5 h-5 text-saudi-primary" />
                <h2 className="text-lg font-bold text-gray-900 arabic-heading">
                  {t.title}
                </h2>
                {totalItems > 0 && (
                  <span className="bg-saudi-primary text-white text-xs font-bold rounded-full px-2 py-1">
                    {totalItems}
                  </span>
                )}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={closeCart}
                className="w-8 h-8 p-0 hover:bg-gray-100"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-hidden flex flex-col">
              {items.length === 0 ? (
                <EmptyCart />
              ) : (
                <>
                  {/* Items List */}
                  <div className="flex-1 overflow-y-auto">
                    <motion.div layout className="divide-y divide-gray-100">
                      {items.map((item) => (
                        <CartItem key={item.id} item={item} />
                      ))}
                    </motion.div>
                  </div>

                  {/* Footer */}
                  <div className="border-t border-gray-200 p-6 bg-gray-50">
                    {/* Clear Cart Button */}
                    {items.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClearCart}
                        className="w-full mb-4 text-gray-600 hover:text-red-600 hover:bg-red-50 arabic-text"
                      >
                        {t.clearCart}
                      </Button>
                    )}

                    {/* Total */}
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 arabic-text">{t.subtotal}:</span>
                        <span className="font-medium">{formatPrice(totalPrice)}</span>
                      </div>
                      <div className="flex justify-between items-center text-lg font-bold">
                        <span className="arabic-heading">{t.total}:</span>
                        <span className="text-saudi-primary">{formatPrice(totalPrice)}</span>
                      </div>
                    </div>

                    {/* Checkout Button */}
                    <Button
                      onClick={handleCheckout}
                      disabled={!isAuthenticated}
                      className="w-full bg-saudi-primary hover:bg-saudi-secondary text-white py-3 arabic-text"
                    >
                      {t.checkout}
                    </Button>

                    {!isAuthenticated && (
                      <p className="text-xs text-gray-500 text-center mt-2 arabic-text">
                        {t.loginRequired}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
