"use client"

import React from 'react'
import { Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PasswordStrengthProps {
  password: string
  className?: string
}

interface PasswordRequirement {
  label: string
  test: (password: string) => boolean
}

const requirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
  },
  {
    label: 'Contains uppercase letter',
    test: (password) => /[A-Z]/.test(password),
  },
  {
    label: 'Contains lowercase letter',
    test: (password) => /[a-z]/.test(password),
  },
  {
    label: 'Contains number',
    test: (password) => /\d/.test(password),
  },
  {
    label: 'Contains special character',
    test: (password) => /[@$!%*?&]/.test(password),
  },
]

const requirementsArabic: PasswordRequirement[] = [
  {
    label: '8 أحرف على الأقل',
    test: (password) => password.length >= 8,
  },
  {
    label: 'يحتوي على حرف كبير',
    test: (password) => /[A-Z]/.test(password),
  },
  {
    label: 'يحتوي على حرف صغير',
    test: (password) => /[a-z]/.test(password),
  },
  {
    label: 'يحتوي على رقم',
    test: (password) => /\d/.test(password),
  },
  {
    label: 'يحتوي على رمز خاص',
    test: (password) => /[@$!%*?&]/.test(password),
  },
]

export default function PasswordStrength({ password, className }: PasswordStrengthProps) {
  // Use Arabic requirements for now (can be made dynamic based on locale)
  const currentRequirements = requirementsArabic
  
  const metRequirements = currentRequirements.filter(req => req.test(password))
  const strength = metRequirements.length
  const maxStrength = currentRequirements.length

  // Don't show if password is empty
  if (!password) {
    return null
  }

  // Calculate strength percentage and color
  const strengthPercentage = (strength / maxStrength) * 100
  const getStrengthColor = () => {
    if (strength <= 2) return 'bg-red-500'
    if (strength <= 3) return 'bg-yellow-500'
    if (strength <= 4) return 'bg-blue-500'
    return 'bg-green-500'
  }

  const getStrengthLabel = () => {
    if (strength <= 2) return 'ضعيف'
    if (strength <= 3) return 'متوسط'
    if (strength <= 4) return 'جيد'
    return 'قوي'
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 arabic-text">
            قوة كلمة المرور
          </span>
          <span className={cn(
            "text-sm font-medium",
            strength <= 2 && "text-red-600",
            strength === 3 && "text-yellow-600",
            strength === 4 && "text-blue-600",
            strength === 5 && "text-green-600"
          )}>
            {getStrengthLabel()}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              "h-2 rounded-full transition-all duration-300",
              getStrengthColor()
            )}
            style={{ width: `${strengthPercentage}%` }}
          />
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-700 arabic-text">
          متطلبات كلمة المرور:
        </p>
        <div className="space-y-1">
          {currentRequirements.map((requirement, index) => {
            const isMet = requirement.test(password)
            return (
              <div
                key={index}
                className={cn(
                  "flex items-center space-x-2 rtl:space-x-reverse text-sm transition-colors duration-200",
                  isMet ? "text-green-600" : "text-gray-500"
                )}
              >
                <div className={cn(
                  "flex-shrink-0 w-4 h-4 rounded-full flex items-center justify-center",
                  isMet ? "bg-green-100" : "bg-gray-100"
                )}>
                  {isMet ? (
                    <Check className="w-3 h-3 text-green-600" />
                  ) : (
                    <X className="w-3 h-3 text-gray-400" />
                  )}
                </div>
                <span className="arabic-text">{requirement.label}</span>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
