"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Star, Heart, Eye, ChevronLeft, ChevronRight } from 'lucide-react'
import { UnifiedButton, IconButton } from '@/components/ui/UnifiedButton'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { FEATURED_TEMPLATES, SITE_CONFIG } from '@/lib/constants'

export default function FeaturedTemplatesSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [favorites, setFavorites] = useState<string[]>([])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % FEATURED_TEMPLATES.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + FEATURED_TEMPLATES.length) % FEATURED_TEMPLATES.length)
  }

  const toggleFavorite = (templateId: string) => {
    setFavorites(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    )
  }

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-[#064635]/10 text-[#064635] px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Star className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
            <span className="arabic-text">الأكثر طلباً</span>
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
            القوالب المميزة
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic-text leading-relaxed">
            اكتشف أفضل القوالب المختارة بعناية من فريقنا المتخصص، مصممة لتلبية احتياجات المطورين والمصممين العرب
          </p>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center mt-8 space-x-4 rtl:space-x-reverse">
            <button
              onClick={prevSlide}
              className="group flex items-center justify-center w-12 h-12 bg-white border-2 border-gray-200 rounded-full hover:border-[#064635] hover:bg-[#064635] transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ChevronRight className="w-5 h-5 text-gray-600 group-hover:text-white rtl:rotate-180 transition-colors duration-300" />
            </button>
            
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-[#064635] rounded-full"></div>
              <div className="w-8 h-2 bg-[#064635]/20 rounded-full"></div>
              <div className="w-2 h-2 bg-[#064635]/40 rounded-full"></div>
            </div>
            
            <button
              onClick={nextSlide}
              className="group flex items-center justify-center w-12 h-12 bg-white border-2 border-gray-200 rounded-full hover:border-[#064635] hover:bg-[#064635] transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ChevronLeft className="w-5 h-5 text-gray-600 group-hover:text-white rtl:rotate-180 transition-colors duration-300" />
            </button>
          </div>
        </div>

        {/* Enhanced Templates Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {FEATURED_TEMPLATES.map((template, index) => (
            <div
              key={template.id}
              className="group bg-white rounded-3xl border border-gray-100 overflow-hidden hover:shadow-2xl hover:shadow-[#064635]/10 hover:border-[#064635]/20 transition-all duration-500 transform hover:-translate-y-3 hover:scale-[1.02]"
            >
              {/* Enhanced Template Image */}
              <div className="relative aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                <Image
                  src={template.image}
                  alt={template.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-700"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Enhanced Preview Button */}
                <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button className="h-10 w-10 bg-white/95 hover:bg-white shadow-xl backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:text-[#064635] transition-all duration-200 hover:scale-110">
                          <Eye className="w-5 h-5" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p className="arabic-text">معاينة مباشرة</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Enhanced Badges */}
                <div className="absolute top-4 left-4 rtl:left-auto rtl:right-4 flex flex-col gap-2">
                  {template.bestseller && (
                    <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm border border-orange-400/20">
                      <span className="arabic-text">الأكثر مبيعاً</span>
                    </div>
                  )}
                  {template.featured && (
                    <div className="bg-gradient-to-r from-[#064635] to-[#053d2f] text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm border border-[#064635]/20">
                      <span className="arabic-text">مميز</span>
                    </div>
                  )}
                </div>

                {/* Enhanced Favorite Button */}
                <div className="absolute bottom-4 right-4 rtl:right-auto rtl:left-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <button
                    onClick={() => toggleFavorite(template.id)}
                    className={`h-10 w-10 rounded-full shadow-xl backdrop-blur-sm transition-all duration-200 hover:scale-110 ${
                      favorites.includes(template.id)
                        ? 'bg-red-500 hover:bg-red-600 text-white border border-red-400/20'
                        : 'bg-white/95 hover:bg-white text-gray-600 hover:text-red-500 border border-white/20'
                    }`}
                  >
                    <Heart className={`w-5 h-5 mx-auto ${favorites.includes(template.id) ? 'fill-current' : ''}`} />
                  </button>
                </div>
              </div>

              {/* Enhanced Template Info */}
              <div className="p-6 space-y-4">
                {/* Enhanced Title */}
                <h3 className="text-xl font-bold text-gray-900 mb-2 arabic-heading group-hover:text-[#064635] transition-colors duration-300 leading-tight">
                  {template.title}
                </h3>

                {/* Enhanced Description */}
                <p className="text-gray-600 text-sm arabic-text leading-relaxed line-clamp-2">
                  {template.description}
                </p>

                {/* Enhanced Tags */}
                <div className="flex flex-wrap gap-2">
                  {template.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 text-xs px-3 py-1.5 rounded-full border border-gray-200 hover:border-[#064635]/20 transition-colors duration-200"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 2 && (
                    <span className="bg-gradient-to-r from-[#064635]/5 to-[#064635]/10 text-[#064635] text-xs px-3 py-1.5 rounded-full border border-[#064635]/20 font-medium">
                      +{template.tags.length - 2}
                    </span>
                  )}
                </div>

                {/* Enhanced Rating & Stats */}
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(template.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm font-semibold text-gray-700">
                      {template.rating}
                    </span>
                    <span className="text-xs text-gray-500 arabic-text">
                      ({template.reviewCount})
                    </span>
                  </div>
                </div>

                {/* Enhanced Price & CTA */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                  <div className="flex flex-col">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <span className="text-2xl font-bold text-[#064635]">
                        {template.price}
                      </span>
                      <span className="text-sm text-gray-600 font-medium">
                        {SITE_CONFIG.currencySymbol}
                      </span>
                    </div>
                    {template.originalPrice && (
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="text-sm text-gray-500 line-through">
                          {template.originalPrice} {SITE_CONFIG.currencySymbol}
                        </span>
                        <span className="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded-full font-medium">
                          -{Math.round(((template.originalPrice - template.price) / template.originalPrice) * 100)}%
                        </span>
                      </div>
                    )}
                  </div>

                  <Link href={`/templates/${template.id}`}>
                    <button className="bg-gradient-to-r from-[#064635] to-[#053d2f] hover:from-[#053d2f] hover:to-[#042a23] text-white px-6 py-2.5 rounded-xl font-medium text-sm arabic-text transition-all duration-300 hover:shadow-lg hover:shadow-[#064635]/25 hover:-translate-y-0.5 active:translate-y-0">
                      عرض التفاصيل
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
