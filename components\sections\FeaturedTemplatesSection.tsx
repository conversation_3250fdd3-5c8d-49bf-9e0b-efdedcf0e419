"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Star, Heart, Eye, TrendingUp, ShoppingCart, Check } from 'lucide-react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useCartStore } from '@/stores/cartStore'
import { useI18n } from '@/contexts/I18nContext'
import { toast } from 'sonner'
import { motion, AnimatePresence } from 'framer-motion'
import { FEATURED_TEMPLATES, SITE_CONFIG } from '@/lib/constants'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

export default function FeaturedTemplatesSection() {
  const [favorites, setFavorites] = useState<string[]>([])
  const [addedToCart, setAddedToCart] = useState<string[]>([])
  const { locale, t } = useI18n()
  const { addItem, items, openCart } = useCartStore()

  const toggleFavorite = (templateId: string) => {
    setFavorites(prev =>
      prev.includes(templateId)
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    )
  }

  const handleAddToCart = (template: any) => {
    const cartItem = {
      id: template.id,
      title: template.title,
      titleAr: template.titleAr || template.title,
      price: template.price,
      image: template.image,
      category: template.category,
      tags: template.tags
    }

    addItem(cartItem)
    setAddedToCart(prev => [...prev, template.id])
    toast.success(t.cart.itemAdded)

    // Remove from addedToCart state after 3 seconds
    setTimeout(() => {
      setAddedToCart(prev => prev.filter(id => id !== template.id))
    }, 3000)

    // Optional: Open cart drawer after adding item
    setTimeout(() => {
      openCart()
    }, 500)
  }

  const isInCart = (templateId: string) => {
    return items.some(item => item.id === templateId)
  }

  const getBadgeIcon = (type: string) => {
    switch (type) {
      case 'featured':
        return '⭐'
      case 'bestseller':
        return '🔥'
      case 'new':
        return '✨'
      default:
        return ''
    }
  }

  const getBadgeStyle = (type: string) => {
    switch (type) {
      case 'featured':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'bestseller':
        return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'new':
        return 'bg-green-100 text-green-700 border-green-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getBadgeText = (type: string) => {
    const texts = {
      featured: locale === 'ar' ? 'مميز' : 'Featured',
      bestseller: locale === 'ar' ? 'الأكثر مبيعًا' : 'Bestseller',
      new: locale === 'ar' ? 'جديد' : 'New'
    }
    return texts[type as keyof typeof texts] || type
  }

  return (
    <div className="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-saudi-primary/10 text-saudi-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <TrendingUp className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
            <span className="arabic-text">الأكثر طلباً</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
            القوالب المميزة
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic-text leading-relaxed">
            اكتشف أفضل القوالب المختارة بعناية من فريقنا المتخصص، مصممة لتلبية احتياجات المطورين والمصممين العرب
          </p>
        </div>

        {/* Enhanced Swiper Carousel */}
        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={24}
            slidesPerView={1}
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              bulletClass: 'swiper-pagination-bullet !bg-gray-300 !w-3 !h-3 !mx-1',
              bulletActiveClass: 'swiper-pagination-bullet-active !bg-saudi-primary !w-8 !rounded-full',
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 24,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 28,
              },
              1280: {
                slidesPerView: 4,
                spaceBetween: 32,
              },
              1536: {
                slidesPerView: 4,
                spaceBetween: 36,
              },
            }}

            dir="rtl"
            className="!pb-16"
            loop={true}
            centeredSlides={false}
            watchSlidesProgress={true}
          >
            {FEATURED_TEMPLATES.map((template, index) => (
              <SwiperSlide key={template.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -4, scale: 1.02 }}
                  className="group"
                >
                  <Card className="overflow-hidden rounded-xl shadow-semantic-lg hover:shadow-semantic-xl transition-all duration-300 cursor-pointer">
                    {/* Template Image with Fixed Aspect Ratio */}
                    <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-surface-secondary to-surface-tertiary">
                      <Image
                        src={template.image}
                        alt={template.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />

                      {/* Overlapping Price Badge - Bottom Left */}
                      <div className="absolute bottom-3 left-3 rtl:left-auto rtl:right-3 z-10">
                        <div className="bg-surface-primary text-primary shadow-semantic-md px-3 py-1.5 rounded-full text-sm font-medium border border-default">
                          {template.originalPrice && (
                            <span className="text-xs text-tertiary line-through mr-2 rtl:mr-0 rtl:ml-2">
                              {template.originalPrice} {SITE_CONFIG.currencySymbol}
                            </span>
                          )}
                          <span className="text-accent font-bold">
                            {template.price} {SITE_CONFIG.currencySymbol}
                          </span>
                          {template.originalPrice && (
                            <Badge variant="secondary" className="ml-2 rtl:ml-0 rtl:mr-2 bg-state-success text-inverse text-xs">
                              خصم {Math.round(((template.originalPrice - template.price) / template.originalPrice) * 100)}%
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Pure Floating Icons - Bottom Right */}
                      <div className="absolute bottom-3 right-3 rtl:right-auto rtl:left-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {/* Preview Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link href={`/templates/${template.id}`} target="_blank">
                                <motion.button
                                  whileHover={{ scale: 1.1, y: -2 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="w-10 h-10 bg-transparent text-inverse drop-shadow-lg hover:drop-shadow-xl rounded-full flex items-center justify-center transition-all duration-200"
                                >
                                  <Eye className="w-5 h-5" />
                                </motion.button>
                              </Link>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">{t.cart.preview}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        {/* Favorite Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <motion.button
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => toggleFavorite(template.id)}
                                className={`w-10 h-10 bg-transparent rounded-full flex items-center justify-center drop-shadow-lg hover:drop-shadow-xl transition-all duration-200 ${
                                  favorites.includes(template.id)
                                    ? 'text-state-error'
                                    : 'text-inverse hover:text-state-error'
                                }`}
                              >
                                <Heart className={`w-5 h-5 ${favorites.includes(template.id) ? 'fill-current' : ''}`} />
                              </motion.button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">
                                {favorites.includes(template.id) ? t.cart.unfavorite : t.cart.favorite}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        {/* Cart Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <motion.button
                                whileHover={{ scale: 1.1, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleAddToCart(template)}
                                disabled={isInCart(template.id)}
                                className={`w-10 h-10 bg-transparent rounded-full flex items-center justify-center drop-shadow-lg hover:drop-shadow-xl transition-all duration-200 ${
                                  isInCart(template.id) || addedToCart.includes(template.id)
                                    ? 'text-state-success'
                                    : 'text-inverse hover:text-accent'
                                }`}
                              >
                                <AnimatePresence mode="wait">
                                  {isInCart(template.id) || addedToCart.includes(template.id) ? (
                                    <motion.div
                                      key="check"
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      exit={{ scale: 0 }}
                                    >
                                      <Check className="w-5 h-5" />
                                    </motion.div>
                                  ) : (
                                    <motion.div
                                      key="cart"
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      exit={{ scale: 0 }}
                                    >
                                      <ShoppingCart className="w-5 h-5" />
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </motion.button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">
                                {isInCart(template.id) ? t.cart.inCart : addedToCart.includes(template.id) ? t.cart.itemAdded : t.cart.addToCart}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>

                    </div>

                    {/* Centered Card Body */}
                    <CardContent className="p-4 text-center space-y-3">
                      <CardTitle className="text-lg font-bold text-primary arabic-heading line-clamp-2">
                        {template.title}
                      </CardTitle>

                      {/* Technology Description */}
                      <div className="text-sm text-secondary arabic-text">
                        {template.tags.slice(0, 2).join(' + ')}
                      </div>

                      {/* Status Badges */}
                      <div className="flex justify-center gap-2">
                        {template.featured && (
                          <Badge className="bg-badge-featured-bg text-badge-featured-text text-xs">
                            {locale === 'ar' ? 'مميز' : 'Featured'}
                          </Badge>
                        )}
                        {template.bestseller && (
                          <Badge className="bg-badge-bestseller-bg text-badge-bestseller-text text-xs">
                            {locale === 'ar' ? 'الأكثر مبيعًا' : 'Bestseller'}
                          </Badge>
                        )}
                        {template.isNew && (
                          <Badge className="bg-badge-new-bg text-badge-new-text text-xs">
                            {locale === 'ar' ? 'جديد' : 'New'}
                          </Badge>
                        )}
                      </div>

                      {/* CTA Button */}
                      <Button
                        variant="outline"
                        asChild
                        className="w-full rounded-xl border-accent text-accent hover:bg-surface-accent-light"
                      >
                        <Link href={`/templates/${template.id}`}>
                          <span className="arabic-text">
                            {locale === 'ar' ? 'تفاصيل القالب' : 'Template Details'}
                          </span>
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>


        </div>

        {/* Enhanced Bottom CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-saudi-primary/5 via-saudi-primary/10 to-saudi-primary/5 rounded-3xl p-8 border border-saudi-primary/10">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4 arabic-heading">
                استكشف المزيد من القوالب المميزة
              </h3>
              <p className="text-gray-600 mb-6 arabic-text">
                لدينا أكثر من 800+ قالب احترافي في انتظارك
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/templates">
                  <button className="bg-gradient-to-r from-saudi-primary to-saudi-secondary hover:from-saudi-secondary hover:to-saudi-700 text-white px-8 py-3 rounded-xl font-medium arabic-text transition-all duration-300 hover:shadow-lg hover:shadow-saudi-primary/25 hover:-translate-y-1 min-w-[200px]">
                    عرض جميع القوالب
                  </button>
                </Link>

                <Link href="/categories">
                  <button className="border-2 border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white px-8 py-3 rounded-xl font-medium arabic-text transition-all duration-300 hover:shadow-lg hover:-translate-y-1 min-w-[200px]">
                    تصفح حسب الفئة
                  </button>
                </Link>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-center space-x-8 rtl:space-x-reverse mt-8 pt-6 border-t border-saudi-primary/10">
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">800+</div>
                  <div className="text-sm text-gray-600 arabic-text">قالب متاح</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">75K+</div>
                  <div className="text-sm text-gray-600 arabic-text">تحميل</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">4.9</div>
                  <div className="text-sm text-gray-600 arabic-text">تقييم المستخدمين</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
};
