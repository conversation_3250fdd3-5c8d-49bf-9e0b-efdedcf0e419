"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Star, Heart, Eye, TrendingUp, ShoppingCart, Check } from 'lucide-react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useCartStore } from '@/stores/cartStore'
import { useI18n } from '@/contexts/I18nContext'
import cartTranslations from '@/lib/translations/cart.json'
import { toast } from 'sonner'
import { motion, AnimatePresence } from 'framer-motion'
import { FEATURED_TEMPLATES, SITE_CONFIG } from '@/lib/constants'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

export default function FeaturedTemplatesSection() {
  const [favorites, setFavorites] = useState<string[]>([])
  const [addedToCart, setAddedToCart] = useState<string[]>([])
  const { locale } = useI18n()
  const t = cartTranslations[locale as keyof typeof cartTranslations]
  const { addItem, items, openCart } = useCartStore()

  const toggleFavorite = (templateId: string) => {
    setFavorites(prev =>
      prev.includes(templateId)
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    )
  }

  const handleAddToCart = (template: any) => {
    const cartItem = {
      id: template.id,
      title: template.title,
      titleAr: template.titleAr || template.title,
      price: template.price,
      image: template.image,
      category: template.category,
      tags: template.tags
    }

    addItem(cartItem)
    setAddedToCart(prev => [...prev, template.id])
    toast.success(t.itemAdded)

    // Remove from addedToCart state after 3 seconds
    setTimeout(() => {
      setAddedToCart(prev => prev.filter(id => id !== template.id))
    }, 3000)

    // Optional: Open cart drawer after adding item
    setTimeout(() => {
      openCart()
    }, 500)
  }

  const isInCart = (templateId: string) => {
    return items.some(item => item.id === templateId)
  }

  const getBadgeIcon = (type: string) => {
    switch (type) {
      case 'featured':
        return '⭐'
      case 'bestseller':
        return '🔥'
      case 'new':
        return '✨'
      default:
        return ''
    }
  }

  const getBadgeStyle = (type: string) => {
    switch (type) {
      case 'featured':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'bestseller':
        return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'new':
        return 'bg-green-100 text-green-700 border-green-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getBadgeText = (type: string) => {
    switch (type) {
      case 'featured':
        return locale === 'ar' ? 'مميز' : 'Featured'
      case 'bestseller':
        return locale === 'ar' ? 'الأكثر مبيعًا' : 'Bestseller'
      case 'new':
        return locale === 'ar' ? 'جديد' : 'New'
      default:
        return type
    }
  }

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-saudi-primary/10 text-saudi-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <TrendingUp className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
            <span className="arabic-text">الأكثر طلباً</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
            القوالب المميزة
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic-text leading-relaxed">
            اكتشف أفضل القوالب المختارة بعناية من فريقنا المتخصص، مصممة لتلبية احتياجات المطورين والمصممين العرب
          </p>
        </div>

        {/* Enhanced Swiper Carousel */}
        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={24}
            slidesPerView={1}
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              bulletClass: 'swiper-pagination-bullet !bg-gray-300 !w-3 !h-3 !mx-1',
              bulletActiveClass: 'swiper-pagination-bullet-active !bg-saudi-primary !w-8 !rounded-full',
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 24,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 28,
              },
              1280: {
                slidesPerView: 4,
                spaceBetween: 32,
              },
              1536: {
                slidesPerView: 4,
                spaceBetween: 36,
              },
            }}

            dir="rtl"
            className="!pb-16"
            loop={true}
            centeredSlides={false}
            watchSlidesProgress={true}
          >
            {FEATURED_TEMPLATES.map((template, index) => (
              <SwiperSlide key={template.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -4, scale: 1.02 }}
                  className="group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                >
                  {/* Template Image with Fixed Aspect Ratio */}
                  <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                    <Image
                      src={template.image}
                      alt={template.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      {/* Action Icons - Bottom Right */}
                      <div className="absolute bottom-3 right-3 rtl:right-auto rtl:left-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        {/* Preview Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link href={`/templates/${template.id}`} target="_blank">
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="w-10 h-10 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:text-saudi-primary shadow-lg transition-colors duration-200"
                                >
                                  <Eye className="w-4 h-4" />
                                </motion.button>
                              </Link>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">{t.preview}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        {/* Favorite Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => toggleFavorite(template.id)}
                                className={`w-10 h-10 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${
                                  favorites.includes(template.id)
                                    ? 'bg-red-500 text-white'
                                    : 'bg-white/95 text-gray-600 hover:text-red-500'
                                }`}
                              >
                                <Heart className={`w-4 h-4 ${favorites.includes(template.id) ? 'fill-current' : ''}`} />
                              </motion.button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">
                                {favorites.includes(template.id) ? t.unfavorite : t.favorite}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        {/* Add to Cart Icon */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleAddToCart(template)}
                                disabled={isInCart(template.id)}
                                className={`w-10 h-10 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${
                                  isInCart(template.id) || addedToCart.includes(template.id)
                                    ? 'bg-green-500 text-white'
                                    : 'bg-white/95 text-gray-600 hover:text-saudi-primary'
                                }`}
                              >
                                <AnimatePresence mode="wait">
                                  {isInCart(template.id) || addedToCart.includes(template.id) ? (
                                    <motion.div
                                      key="check"
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      exit={{ scale: 0 }}
                                    >
                                      <Check className="w-4 h-4" />
                                    </motion.div>
                                  ) : (
                                    <motion.div
                                      key="cart"
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      exit={{ scale: 0 }}
                                    >
                                      <ShoppingCart className="w-4 h-4" />
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </motion.button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="arabic-text">
                                {isInCart(template.id) ? t.inCart : addedToCart.includes(template.id) ? t.addedToCart : t.addToCart}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>

                    {/* Refined Badges - Top Left */}
                    <div className="absolute top-3 left-3 rtl:left-auto rtl:right-3 flex flex-col gap-1">
                      {template.featured && (
                        <motion.span
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.2 }}
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getBadgeStyle('featured')}`}
                        >
                          <span>{getBadgeIcon('featured')}</span>
                          <span className="arabic-text">{getBadgeText('featured')}</span>
                        </motion.span>
                      )}
                      {template.bestseller && (
                        <motion.span
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3 }}
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getBadgeStyle('bestseller')}`}
                        >
                          <span>{getBadgeIcon('bestseller')}</span>
                          <span className="arabic-text">{getBadgeText('bestseller')}</span>
                        </motion.span>
                      )}
                      {template.isNew && (
                        <motion.span
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.4 }}
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getBadgeStyle('new')}`}
                        >
                          <span>{getBadgeIcon('new')}</span>
                          <span className="arabic-text">{getBadgeText('new')}</span>
                        </motion.span>
                      )}
                    </div>
                  </div>

                  {/* Template Info - Fixed Height */}
                  <div className="p-4 h-32 flex flex-col justify-between">
                    {/* Title */}
                    <h3 className="text-lg font-bold text-gray-900 arabic-heading group-hover:text-saudi-primary transition-colors duration-300 line-clamp-2 leading-tight">
                      {template.title}
                    </h3>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-2">
                      {template.tags.slice(0, 2).map((tag) => (
                        <span
                          key={tag}
                          className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded border border-gray-200"
                        >
                          {tag}
                        </span>
                      ))}
                      {template.tags.length > 2 && (
                        <span className="bg-saudi-50 text-saudi-primary text-xs px-2 py-1 rounded border border-saudi-200 font-medium">
                          +{template.tags.length - 2}
                        </span>
                      )}
                    </div>

                    {/* Rating & Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-3 h-3 ${
                                i < Math.floor(template.rating)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm font-medium text-gray-700">
                          {template.rating}
                        </span>
                      </div>

                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <span className="text-lg font-bold text-saudi-primary">
                          {template.price}
                        </span>
                        <span className="text-sm text-gray-600">
                          {SITE_CONFIG.currencySymbol}
                        </span>
                      </div>
                    </div>
                  </div>

                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>


        </div>

        {/* Enhanced Bottom CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-saudi-primary/5 via-saudi-primary/10 to-saudi-primary/5 rounded-3xl p-8 border border-saudi-primary/10">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4 arabic-heading">
                استكشف المزيد من القوالب المميزة
              </h3>
              <p className="text-gray-600 mb-6 arabic-text">
                لدينا أكثر من 800+ قالب احترافي في انتظارك
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/templates">
                  <button className="bg-gradient-to-r from-saudi-primary to-saudi-secondary hover:from-saudi-secondary hover:to-saudi-700 text-white px-8 py-3 rounded-xl font-medium arabic-text transition-all duration-300 hover:shadow-lg hover:shadow-saudi-primary/25 hover:-translate-y-1 min-w-[200px]">
                    عرض جميع القوالب
                  </button>
                </Link>

                <Link href="/categories">
                  <button className="border-2 border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white px-8 py-3 rounded-xl font-medium arabic-text transition-all duration-300 hover:shadow-lg hover:-translate-y-1 min-w-[200px]">
                    تصفح حسب الفئة
                  </button>
                </Link>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-center space-x-8 rtl:space-x-reverse mt-8 pt-6 border-t border-saudi-primary/10">
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">800+</div>
                  <div className="text-sm text-gray-600 arabic-text">قالب متاح</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">75K+</div>
                  <div className="text-sm text-gray-600 arabic-text">تحميل</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-saudi-primary">4.9</div>
                  <div className="text-sm text-gray-600 arabic-text">تقييم المستخدمين</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
