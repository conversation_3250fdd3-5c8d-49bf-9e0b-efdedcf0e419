"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Star, Heart, Eye, ChevronLeft, ChevronRight } from 'lucide-react'
import { UnifiedButton, IconButton } from '@/components/ui/UnifiedButton'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { FEATURED_TEMPLATES, SITE_CONFIG } from '@/lib/constants'

export default function FeaturedTemplatesSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [favorites, setFavorites] = useState<string[]>([])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % FEATURED_TEMPLATES.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + FEATURED_TEMPLATES.length) % FEATURED_TEMPLATES.length)
  }

  const toggleFavorite = (templateId: string) => {
    setFavorites(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 arabic-heading">
              القوالب المميزة
            </h2>
            <p className="text-xl text-gray-600 arabic-text">
              اكتشف أفضل القوالب المختارة بعناية من فريقنا
            </p>
          </div>

          {/* Navigation Arrows */}
          <div className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
            <UnifiedButton
              variant="secondary"
              size="icon"
              rounded="full"
              onClick={prevSlide}
            >
              <ChevronRight className="w-4 h-4 rtl:rotate-180" />
            </UnifiedButton>
            <UnifiedButton
              variant="secondary"
              size="icon"
              rounded="full"
              onClick={nextSlide}
            >
              <ChevronLeft className="w-4 h-4 rtl:rotate-180" />
            </UnifiedButton>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {FEATURED_TEMPLATES.map((template, index) => (
            <div
              key={template.id}
              className="group bg-white rounded-2xl border border-gray-200 overflow-hidden hover:shadow-2xl hover:border-green-200 transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Template Image */}
              <div className="relative aspect-video bg-gray-100 overflow-hidden">
                <Image
                  src={template.image}
                  alt={template.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />

                {/* Preview Button - Top Right Corner */}
                <div className="absolute top-4 right-4 rtl:right-auto rtl:left-4">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button className="h-8 w-8 bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 hover:text-[#064635] transition-colors duration-200">
                          <Eye className="w-4 h-4" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="arabic-text">معاينة</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Badges */}
                <div className="absolute top-4 left-4 rtl:left-auto rtl:right-4 flex flex-col gap-2">
                  {template.bestseller && (
                    <div className="bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                      الأكثر مبيعاً
                    </div>
                  )}
                  {template.featured && (
                    <div className="bg-[#064635] text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                      مميز
                    </div>
                  )}
                </div>

                {/* Favorite Button - Bottom Right */}
                <div className="absolute bottom-4 right-4 rtl:right-auto rtl:left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <IconButton
                    size="icon-sm"
                    onClick={() => toggleFavorite(template.id)}
                    className={`shadow-lg backdrop-blur-sm ${
                      favorites.includes(template.id)
                        ? 'bg-red-500 hover:bg-red-600 text-white'
                        : 'bg-white/90 hover:bg-white'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${favorites.includes(template.id) ? 'fill-current' : ''}`} />
                  </IconButton>
                </div>
              </div>

              {/* Template Info */}
              <div className="p-6">
                {/* Title */}
                <h3 className="text-lg font-bold text-gray-900 mb-2 arabic-heading group-hover:text-saudi-primary transition-colors duration-300">
                  {template.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-4 arabic-text line-clamp-2">
                  {template.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {template.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 2 && (
                    <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                      +{template.tags.length - 2}
                    </span>
                  )}
                </div>

                {/* Rating & Reviews */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(template.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600 mr-2 rtl:mr-0 rtl:ml-2">
                      {template.rating}
                    </span>
                    <span className="text-sm text-gray-500 arabic-text">
                      ({template.reviewCount} تقييم)
                    </span>
                  </div>
                </div>

                {/* Price & CTA */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-2xl font-bold text-saudi-primary">
                      {template.price} {SITE_CONFIG.currencySymbol}
                    </span>
                    {template.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {template.originalPrice} {SITE_CONFIG.currencySymbol}
                      </span>
                    )}
                  </div>

                  <UnifiedButton
                    asChild
                    size="sm"
                    variant="primary"
                  >
                    <Link href={`/templates/${template.id}`} className="arabic-text">
                      عرض التفاصيل
                    </Link>
                  </UnifiedButton>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Navigation Dots */}
        <div className="flex justify-center mt-8 md:hidden">
          <div className="flex space-x-2 rtl:space-x-reverse">
            {FEATURED_TEMPLATES.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                  index === currentSlide ? 'bg-saudi-primary' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* View All CTA */}
        <div className="text-center mt-12">
          <UnifiedButton asChild variant="secondary" size="lg">
            <Link href="/templates" className="arabic-text">
              عرض جميع القوالب
            </Link>
          </UnifiedButton>
        </div>
      </div>
    </section>
  )
}
