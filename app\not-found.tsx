"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Search, Home, Grid3X3, Mail, ArrowLeft, Sparkles, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function NotFound() {
  const [searchQuery, setSearchQuery] = useState('')
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/templates?search=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  const quickLinks = [
    {
      href: '/',
      icon: Home,
      label: 'الصفحة الرئيسية',
      labelEn: 'Homepage',
      description: 'العودة إلى الصفحة الرئيسية'
    },
    {
      href: '/templates',
      icon: Grid3X3,
      label: 'القوالب',
      labelEn: 'Templates',
      description: 'تصفح جميع القوالب المتاحة'
    },
    {
      href: '/categories',
      icon: Grid3X3,
      label: 'الفئات',
      labelEn: 'Categories',
      description: 'استكشف القوالب حسب الفئة'
    },
    {
      href: '/contact',
      icon: Mail,
      label: 'اتصل بنا',
      labelEn: 'Contact',
      description: 'تواصل مع فريق الدعم'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-saudi-50 flex items-center justify-center px-4 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-96 h-96 bg-saudi-primary rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-saudi-primary rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        {/* Animated 404 Number */}
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-8"
        >
          <div className="text-8xl lg:text-9xl font-bold text-saudi-primary mb-4 relative">
            <span className="relative z-10">404</span>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 text-saudi-primary/10 text-8xl lg:text-9xl"
            >
              <Sparkles className="w-full h-full" />
            </motion.div>
          </div>
        </motion.div>

        {/* Error Messages */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-12"
        >
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 arabic-heading">
            عذراً، الصفحة غير موجودة
          </h1>
          <p className="text-xl text-gray-600 mb-2 arabic-text">
            لا يمكننا العثور على الصفحة التي تبحث عنها
          </p>
          <p className="text-lg text-gray-500">
            Sorry, we couldn't find the page you're looking for.
          </p>
        </motion.div>

        {/* Search Section */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-12"
        >
          <div className="bg-white rounded-2xl border border-gray-200 p-8 shadow-lg max-w-2xl mx-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4 arabic-heading">
              ابحث عن القوالب
            </h2>
            <form onSubmit={handleSearch} className="flex gap-3">
              <div className="flex-1 relative">
                <Search className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="ابحث عن القوالب..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10 rtl:pr-3 rtl:pl-10 text-right rtl:text-right arabic-text"
                />
              </div>
              <Button
                type="submit"
                className="bg-saudi-primary hover:bg-saudi-secondary text-white px-6"
              >
                بحث
              </Button>
            </form>
          </div>
        </motion.div>

        {/* Quick Links */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-8 arabic-heading">
            روابط مفيدة
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickLinks.map((link, index) => (
              <motion.div
                key={link.href}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Link href={link.href}>
                  <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-xl hover:border-saudi-primary/20 transition-all duration-300">
                    <div className="w-12 h-12 bg-saudi-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:bg-saudi-primary group-hover:scale-110 transition-all duration-300">
                      <link.icon className="w-6 h-6 text-saudi-primary group-hover:text-white" />
                    </div>
                    <h3 className="font-bold text-gray-900 mb-2 arabic-heading group-hover:text-saudi-primary transition-colors">
                      {link.label}
                    </h3>
                    <p className="text-sm text-gray-600 arabic-text">
                      {link.description}
                    </p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Back Button */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <Button
            onClick={() => router.back()}
            variant="outline"
            className="inline-flex items-center space-x-2 rtl:space-x-reverse border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white transition-all duration-300"
          >
            <ArrowLeft className="w-4 h-4 rtl:rotate-180" />
            <span className="arabic-text">العودة للصفحة السابقة</span>
          </Button>
        </motion.div>

        {/* Decorative Elements */}
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-20 right-20 text-saudi-primary/20"
        >
          <RefreshCw className="w-8 h-8" />
        </motion.div>

        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          className="absolute bottom-20 left-20 text-saudi-primary/20"
        >
          <Sparkles className="w-6 h-6" />
        </motion.div>
      </div>
    </div>
  )
}
