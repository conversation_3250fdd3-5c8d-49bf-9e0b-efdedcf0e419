"use client"

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from './context'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { useI18n } from '@/contexts/I18nContext'

// Hook for handling authentication redirects
export function useAuthRedirect() {
  const { state } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (state.initialized && state.user) {
      const redirectTo = searchParams.get('redirectTo') || '/'
      router.push(redirectTo)
    }
  }, [state.initialized, state.user, router, searchParams])
}

// Hook for protected routes
export function useRequireAuth(redirectTo: string = '/auth/login') {
  const { state } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (state.initialized && !state.user) {
      router.push(`${redirectTo}?redirectTo=${encodeURIComponent(window.location.pathname)}`)
    }
  }, [state.initialized, state.user, router, redirectTo])

  return {
    user: state.user,
    profile: state.profile,
    loading: state.loading || !state.initialized,
    isAuthenticated: !!state.user,
  }
}

// Hook for guest-only routes (redirect authenticated users)
export function useGuestOnly(redirectTo: string = '/') {
  const { state } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (state.initialized && state.user) {
      router.push(redirectTo)
    }
  }, [state.initialized, state.user, router, redirectTo])

  return {
    loading: state.loading || !state.initialized,
    isGuest: !state.user,
  }
}

// Hook for form submission with loading states
export function useAuthForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = useCallback(async (
    submitFn: () => Promise<{ error?: any }>,
    onSuccess?: () => void,
    onError?: (error: any) => void
  ) => {
    setIsSubmitting(true)
    setErrors({})

    try {
      const { error } = await submitFn()

      if (error) {
        const errorMessage = getErrorMessage(error)
        setErrors({ general: errorMessage })
        toast.error(errorMessage)
        onError?.(error)
      } else {
        onSuccess?.()
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred'
      setErrors({ general: errorMessage })
      toast.error(errorMessage)
      onError?.(error)
    } finally {
      setIsSubmitting(false)
    }
  }, [])

  const clearErrors = useCallback(() => {
    setErrors({})
  }, [])

  const setFieldError = useCallback((field: string, message: string) => {
    setErrors(prev => ({ ...prev, [field]: message }))
  }, [])

  return {
    isSubmitting,
    errors,
    handleSubmit,
    clearErrors,
    setFieldError,
  }
}

// Hook for password strength validation
export function usePasswordStrength() {
  const [password, setPassword] = useState('')
  const [strength, setStrength] = useState({
    score: 0,
    feedback: [] as string[],
    isValid: false,
  })

  useEffect(() => {
    if (!password) {
      setStrength({ score: 0, feedback: [], isValid: false })
      return
    }

    const feedback: string[] = []
    let score = 0

    if (password.length >= 8) score += 1
    else feedback.push('At least 8 characters')

    if (password.length >= 12) score += 1
    else feedback.push('12+ characters recommended')

    if (/[a-z]/.test(password)) score += 1
    else feedback.push('Include lowercase letters')

    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('Include uppercase letters')

    if (/\d/.test(password)) score += 1
    else feedback.push('Include numbers')

    if (/[@$!%*?&]/.test(password)) score += 1
    else feedback.push('Include special characters')

    setStrength({
      score,
      feedback: score >= 4 ? [] : feedback,
      isValid: score >= 4,
    })
  }, [password])

  return {
    password,
    setPassword,
    strength,
  }
}

// Hook for session management
export function useSession() {
  const { state, refreshSession, signOut } = useAuth()
  const { t } = useI18n()

  const refresh = useCallback(async () => {
    try {
      await refreshSession()
    } catch (error) {
      console.error('Failed to refresh session:', error)
    }
  }, [refreshSession])

  const logout = useCallback(async () => {
    try {
      await signOut()
      toast.success(t.auth.success.loggedOut)
    } catch (error) {
      console.error('Failed to sign out:', error)
      toast.error(t.auth.errors.networkError)
    }
  }, [signOut, t])

  return {
    user: state.user,
    profile: state.profile,
    loading: state.loading,
    initialized: state.initialized,
    isAuthenticated: !!state.user,
    refresh,
    logout,
  }
}

// Utility function to get user-friendly error messages
function getErrorMessage(error: any): string {
  if (!error) return 'An unknown error occurred'

  // Supabase error codes
  const errorMessages: Record<string, string> = {
    'invalid_credentials': 'Invalid email or password',
    'email_not_confirmed': 'Please confirm your email address',
    'signup_disabled': 'Sign up is currently disabled',
    'email_address_invalid': 'Please enter a valid email address',
    'password_too_short': 'Password must be at least 8 characters',
    'weak_password': 'Password is too weak',
    'user_already_registered': 'An account with this email already exists',
    'too_many_requests': 'Too many requests. Please try again later',
    'captcha_failed': 'Captcha verification failed',
    'email_address_not_authorized': 'Email address not authorized',
    'email_change_token_new_invalid': 'Invalid email change token',
    'email_change_token_expired': 'Email change token expired',
    'same_password': 'New password must be different from current password',
    'session_not_found': 'Session not found. Please sign in again',
    'user_not_found': 'User not found',
    'flow_state_not_found': 'Flow state not found',
    'flow_state_expired': 'Flow state expired',
    'signup_disabled': 'Sign up is currently disabled',
  }

  // Check for specific error codes
  if (error.code && errorMessages[error.code]) {
    return errorMessages[error.code]
  }

  // Check for error message
  if (error.message) {
    return error.message
  }

  // Fallback
  return 'An unexpected error occurred'
}

// Hook for real-time validation
export function useFieldValidation() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})

  const validateField = useCallback((
    fieldName: string,
    value: any,
    validator: (value: any) => string | null
  ) => {
    const error = validator(value)
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error || '',
    }))
    return !error
  }, [])

  const clearFieldError = useCallback((fieldName: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: '',
    }))
  }, [])

  const clearAllErrors = useCallback(() => {
    setFieldErrors({})
  }, [])

  return {
    fieldErrors,
    validateField,
    clearFieldError,
    clearAllErrors,
  }
}
