import React from "react";

interface SimpleAuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
}

export default function SimpleAuthLayout({ children, title, subtitle }: SimpleAuthLayoutProps) {
  return React.createElement(
    "div",
    { className: "min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" },
    React.createElement(
      "div",
      { className: "max-w-md w-full space-y-8" },
      React.createElement(
        "div",
        null,
        React.createElement(
          "h2",
          { className: "mt-6 text-center text-3xl font-extrabold text-gray-900 arabic-heading" },
          title
        ),
        React.createElement(
          "p",
          { className: "mt-2 text-center text-sm text-gray-600 arabic-text" },
          subtitle
        )
      ),
      React.createElement(
        "div",
        { className: "bg-white rounded-lg shadow-md p-8" },
        children
      )
    )
  );
}
