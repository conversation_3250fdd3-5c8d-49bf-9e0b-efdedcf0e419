"use client"

import React from 'react'
import { useGuestOnly } from '@/lib/auth/hooks'
import { Loader2 } from 'lucide-react'

interface GuestRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export default function GuestRoute({ 
  children, 
  fallback,
  redirectTo = '/' 
}: GuestRouteProps) {
  const { loading, isGuest } = useGuestOnly(redirectTo)

  if (loading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#064635]" />
          <p className="text-gray-600 arabic-text">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!isGuest) {
    return null // Redirect is handled by useGuestOnly hook
  }

  return <>{children}</>
}
