"use client"

import React from 'react'
import { useGuestOnly } from '@/lib/auth/hooks'
import { Loader2 } from 'lucide-react'

interface GuestRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export default function GuestRoute({
  children,
  fallback,
  redirectTo = '/'
}: GuestRouteProps) {
  const { loading, isGuest } = useGuestOnly(redirectTo)

  // Skip loading state for instant navigation
  if (!loading && !isGuest) {
    return null // Redirect is handled by useGuestOnly hook
  }

  return <>{children}</>
}
