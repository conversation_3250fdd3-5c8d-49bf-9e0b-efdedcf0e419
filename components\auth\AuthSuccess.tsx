"use client"

import React from 'react'
import { CheckCircle, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AuthSuccessProps {
  message?: string | null
  onDismiss?: () => void
  className?: string
}

export default function AuthSuccess({ message, onDismiss, className }: AuthSuccessProps) {
  if (!message) return null

  return (
    <div className={cn(
      "bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3 rtl:space-x-reverse",
      className
    )}>
      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <p className="text-sm text-green-700 arabic-text">{message}</p>
      </div>
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="text-green-400 hover:text-green-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  )
}
