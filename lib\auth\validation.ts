import { z } from 'zod'

// Phone number validation regex (international format)
const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/

// Password strength validation
const passwordStrengthRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/

// Custom validation functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePhone = (phone: string): boolean => {
  if (!phone || phone.trim() === '') return true // Optional field
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

export const validatePasswordStrength = (password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} => {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) score += 1
  else feedback.push('Password must be at least 8 characters')

  if (password.length >= 12) score += 1
  else feedback.push('Consider using 12+ characters for better security')

  if (/[a-z]/.test(password)) score += 1
  else feedback.push('Include lowercase letters')

  if (/[A-Z]/.test(password)) score += 1
  else feedback.push('Include uppercase letters')

  if (/\d/.test(password)) score += 1
  else feedback.push('Include numbers')

  if (/[@$!%*?&]/.test(password)) score += 1
  else feedback.push('Include special characters (@$!%*?&)')

  return {
    isValid: score >= 4,
    score,
    feedback: score >= 4 ? [] : feedback
  }
}

// Enhanced schemas with custom validation
export const enhancedLoginSchema = z.object({
  email: z.string()
    .min(1, 'Email is required')
    .refine(validateEmail, 'Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false),
})

export const enhancedRegisterSchema = z.object({
  fullName: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .regex(/^[a-zA-Z\u0600-\u06FF\s]+$/, 'Name can only contain letters and spaces'),
  email: z.string()
    .min(1, 'Email is required')
    .refine(validateEmail, 'Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .refine(
      (password) => validatePasswordStrength(password).isValid,
      'Password must include uppercase, lowercase, number, and special character'
    ),
  confirmPassword: z.string(),
  phone: z.string()
    .optional()
    .refine(validatePhone, 'Please enter a valid phone number'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export const enhancedForgotPasswordSchema = z.object({
  email: z.string()
    .min(1, 'Email is required')
    .refine(validateEmail, 'Please enter a valid email address'),
})

export const enhancedResetPasswordSchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .refine(
      (password) => validatePasswordStrength(password).isValid,
      'Password must include uppercase, lowercase, number, and special character'
    ),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export const enhancedUpdateProfileSchema = z.object({
  fullName: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .regex(/^[a-zA-Z\u0600-\u06FF\s]+$/, 'Name can only contain letters and spaces'),
  phone: z.string()
    .optional()
    .refine(validatePhone, 'Please enter a valid phone number'),
})

export const enhancedChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .refine(
      (password) => validatePasswordStrength(password).isValid,
      'Password must include uppercase, lowercase, number, and special character'
    ),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "New password must be different from current password",
  path: ["newPassword"],
})

// Validation error formatter
export const formatValidationErrors = (errors: z.ZodError) => {
  const formattedErrors: Record<string, string> = {}
  
  errors.errors.forEach((error) => {
    const path = error.path.join('.')
    formattedErrors[path] = error.message
  })
  
  return formattedErrors
}

// Real-time validation helpers
export const validateField = (
  schema: z.ZodSchema,
  fieldName: string,
  value: any,
  allValues?: Record<string, any>
): string | null => {
  try {
    const dataToValidate = allValues ? { ...allValues, [fieldName]: value } : { [fieldName]: value }
    schema.parse(dataToValidate)
    return null
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldError = error.errors.find(err => 
        err.path.includes(fieldName)
      )
      return fieldError?.message || null
    }
    return null
  }
}
