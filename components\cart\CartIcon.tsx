"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ShoppingCart } from 'lucide-react'
import { useCartStore } from '@/stores/cartStore'
import { Button } from '@/components/ui/button'

export default function CartIcon() {
  const { getTotalItems, toggleCart } = useCartStore()
  const totalItems = getTotalItems()

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleCart}
        className="relative flex items-center justify-center w-10 h-10 text-gray-700 hover:text-saudi-primary hover:bg-saudi-50 transition-all duration-200 rounded-lg"
      >
        <ShoppingCart className="w-5 h-5" />
        
        {/* Animated Badge */}
        <AnimatePresence>
          {totalItems > 0 && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ 
                type: "spring", 
                stiffness: 500, 
                damping: 30,
                duration: 0.2 
              }}
              className="absolute -top-2 -right-2 bg-saudi-primary text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1 shadow-lg"
            >
              <motion.span
                key={totalItems}
                initial={{ scale: 1.2 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.15 }}
              >
                {totalItems > 99 ? '99+' : totalItems}
              </motion.span>
            </motion.div>
          )}
        </AnimatePresence>
      </Button>
    </div>
  )
}
