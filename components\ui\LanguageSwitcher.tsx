"use client"

import React, { useState } from 'react'
import ReactCountryFlag from 'react-country-flag'
import { ChevronDown, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Language configuration interface
interface Language {
  code: string
  name: string
  nameEn: string
  countryCode: string
  flag: string
}

// Available languages
const LANGUAGES: Language[] = [
  {
    code: 'ar',
    name: 'العربية',
    nameEn: 'Arabic',
    countryCode: 'SA',
    flag: '🇸🇦'
  },
  {
    code: 'en',
    name: 'English',
    nameEn: 'English',
    countryCode: 'US',
    flag: '🇺🇸'
  },
  {
    code: 'fr',
    name: 'Français',
    nameEn: 'French',
    countryCode: 'FR',
    flag: '🇫🇷'
  },
  {
    code: 'ru',
    name: 'Русский',
    nameEn: 'Russian',
    countryCode: 'RU',
    flag: '🇷🇺'
  }
]

interface LanguageSwitcherProps {
  className?: string
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg'
}

export default function LanguageSwitcher({ 
  className = '', 
  variant = 'ghost',
  size = 'sm'
}: LanguageSwitcherProps) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(LANGUAGES[0]) // Default to Arabic

  const handleLanguageChange = (language: Language) => {
    setCurrentLanguage(language)
    console.log('Language changed to:', language.code, language.name)
    
    // Here you would typically:
    // 1. Update the app's language context/state
    // 2. Change the document direction (RTL/LTR)
    // 3. Update localStorage/cookies for persistence
    // 4. Trigger re-render with new language
    
    // Example implementation:
    // updateLanguage(language.code)
    // document.documentElement.dir = language.code === 'ar' ? 'rtl' : 'ltr'
    // localStorage.setItem('preferred-language', language.code)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className={`flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200 px-3 py-2 rounded-lg ${className}`}>
          <ReactCountryFlag
            countryCode={currentLanguage.countryCode}
            svg
            style={{
              width: '1rem',
              height: '0.75rem',
            }}
            title={currentLanguage.nameEn}
          />
          <span className="text-sm font-medium">{currentLanguage.name}</span>
          <ChevronDown className="w-4 h-4" />
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-48">
        {LANGUAGES.map((language) => (
          <DropdownMenuItem 
            key={language.code}
            onClick={() => handleLanguageChange(language)}
            className={`flex items-center space-x-3 rtl:space-x-reverse p-3 cursor-pointer ${
              currentLanguage.code === language.code 
                ? 'bg-green-50 text-green-700' 
                : 'hover:bg-gray-50'
            }`}
          >
            <ReactCountryFlag
              countryCode={language.countryCode}
              svg
              style={{
                width: '1.25rem',
                height: '1rem',
              }}
              title={language.nameEn}
            />
            <div className="flex flex-col">
              <span className="font-medium text-sm">{language.name}</span>
              <span className="text-xs text-gray-500">{language.nameEn}</span>
            </div>
            {currentLanguage.code === language.code && (
              <div className="mr-auto rtl:mr-0 rtl:ml-auto">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Export the languages for use in other components
export { LANGUAGES }
export type { Language }
