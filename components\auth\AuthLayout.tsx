"use client"

import React from 'react'
import Link from 'next/link'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { UnifiedButton } from '@/components/ui/UnifiedButton'

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  rightContent: React.ReactNode
  showBackToHome?: boolean
}

export default function AuthLayout({
  children,
  title,
  subtitle,
  rightContent,
  showBackToHome = true,
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Column - Auth Form */}
      <div className="w-full lg:w-2/5 flex flex-col justify-center px-6 py-12 lg:px-8">
        <div className="mx-auto w-full max-w-sm">
          {/* Back to Home Button */}
          {showBackToHome && (
            <div className="mb-8">
              <UnifiedButton
                asChild
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-[#064635] p-0"
              >
                <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
                  <ArrowLeft className="w-4 h-4 rtl:hidden" />
                  <ArrowRight className="w-4 h-4 hidden rtl:block" />
                  <span className="arabic-text">العودة للرئيسية</span>
                </Link>
              </UnifiedButton>
            </div>
          )}

          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 arabic-heading mb-2">
              {title}
            </h1>
            <p className="text-lg text-gray-600 arabic-text">
              {subtitle}
            </p>
          </div>

          {/* Form Content */}
          <div className="bg-white rounded-2xl border border-gray-200 p-8 shadow-lg">
            {children}
          </div>
        </div>
      </div>

      {/* Right Column - Welcome Content */}
      <div className="hidden lg:flex lg:w-3/5 bg-gradient-to-br from-[#064635] to-[#053d2f] relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
          {rightContent}
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
      </div>
    </div>
  )
}
