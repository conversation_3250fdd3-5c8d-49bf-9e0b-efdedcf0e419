// Common translations used across multiple components
export type CommonTranslations = {
  language: string
  arabic: string
  english: string
  welcome: string
  loading: string
  error: string
  success: string
  cancel: string
  confirm: string
  save: string
  edit: string
  delete: string
  close: string
  back: string
  next: string
  previous: string
  submit: string
  reset: string
  search: string
  filter: string
  sort: string
  view: string
  download: string
  upload: string
  share: string
  copy: string
  paste: string
  cut: string
  select: string
  selectAll: string
  clear: string
  refresh: string
  retry: string
  test: {
    title: string
    subtitle: string
    description: string
    features: {
      title: string
      languageSwitch: string
      persistence: string
      rtl: string
      ltr: string
    }
    currentLanguage: string
    switchPrompt: string
    backToHome: string
  }
}

// Hero section translations
export type HeroTranslations = {
  title: string
  subtitle: string
  description: string
  cta: {
    primary: string
    secondary: string
  }
  stats: {
    templates: string
    developers: string
    rating: string
  }
  badge: string
}

// Navigation/Header translations
export type NavbarTranslations = {
  brand: {
    name: string
    tagline: string
  }
  navigation: {
    home: string
    templates: string
    categories: string
    pricing: string
    support: string
    about: string
    faq: string
    contact: string
  }
  actions: {
    search: string
    searchPlaceholder: string
    login: string
    register: string
    profile: string
    logout: string
  }
  language: {
    selector: string
    current: string
  }
}

// Footer translations
export type FooterTranslations = {
  company: {
    name: string
    description: string
    tagline: string
  }
  links: {
    product: {
      title: string
      templates: string
      categories: string
      pricing: string
      features: string
    }
    support: {
      title: string
      help: string
      documentation: string
      contact: string
      faq: string
    }
    company: {
      title: string
      about: string
      blog: string
      careers: string
      press: string
    }
    legal: {
      title: string
      privacy: string
      terms: string
      cookies: string
      license: string
    }
  }
  newsletter: {
    title: string
    description: string
    placeholder: string
    subscribe: string
    success: string
    error: string
  }
  social: {
    title: string
    facebook: string
    twitter: string
    instagram: string
    linkedin: string
    github: string
  }
  copyright: string
}

// Authentication translations
export type AuthTranslations = {
  login: {
    title: string
    subtitle: string
    description: string
    email: string
    password: string
    rememberMe: string
    forgotPassword: string
    loginButton: string
    noAccount: string
    signUp: string
    welcomeBack: string
    benefits: {
      title: string
      access: string
      downloads: string
      support: string
      updates: string
    }
  }
  register: {
    title: string
    subtitle: string
    description: string
    fullName: string
    email: string
    password: string
    confirmPassword: string
    phone: string
    registerButton: string
    haveAccount: string
    signIn: string
    welcome: string
    benefits: {
      title: string
      templates: string
      quality: string
      community: string
      earnings: string
    }
  }
  forgotPassword: {
    title: string
    subtitle: string
    description: string
    email: string
    sendButton: string
    backToLogin: string
    checkEmail: string
    emailSent: string
    recovery: {
      title: string
      secure: string
      quick: string
      support: string
      privacy: string
    }
  }
  resetPassword: {
    title: string
    subtitle: string
    description: string
    newPassword: string
    confirmPassword: string
    updateButton: string
    backToLogin: string
    success: string
    security: {
      title: string
      encryption: string
      protection: string
      monitoring: string
      compliance: string
    }
  }
  profile: {
    title: string
    subtitle: string
    personalInfo: string
    fullName: string
    email: string
    phone: string
    avatar: string
    updateButton: string
    changePassword: string
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }
  validation: {
    required: string
    email: string
    password: string
    passwordMatch: string
    phone: string
    name: string
  }
  errors: {
    invalidCredentials: string
    emailExists: string
    weakPassword: string
    networkError: string
    serverError: string
    emailNotConfirmed: string
    tooManyRequests: string
  }
  success: {
    registered: string
    loggedIn: string
    loggedOut: string
    passwordReset: string
    passwordUpdated: string
    profileUpdated: string
  }
  loading: {
    signingIn: string
    signingUp: string
    sendingEmail: string
    updatingPassword: string
    updatingProfile: string
  }
  logout: string
  user: string
}

// Combined translations type
export type Translations = {
  common: CommonTranslations
  hero: HeroTranslations
  navbar: NavbarTranslations
  footer: FooterTranslations
  auth: AuthTranslations
}
