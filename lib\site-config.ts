// Site Configuration for NinjaTemplates Arabic Marketplace
import { LucideIcon } from 'lucide-react'

// TopBar Announcement Configuration
export interface SiteAnnouncement {
  type: 'promo' | 'info' | null
  message: string
  messageEn?: string
  link?: string
  linkText?: string
  linkTextEn?: string
  isActive: boolean
  emoji?: string
}

// Navigation Item Interface
export interface NavigationItem {
  name: string
  nameEn?: string
  href: string
  icon?: string
  hasDropdown?: boolean
  dropdownItems?: DropdownItem[]
}

export interface DropdownItem {
  name: string
  nameEn?: string
  href: string
  icon?: string
  description?: string
}

// Current Site Announcement Configuration
export const SITE_ANNOUNCEMENT: SiteAnnouncement = {
  type: 'promo', // Change to 'info' or null to switch modes
  message: 'احصل على خصم 30٪ على جميع قوالب React حتى نهاية الأسبوع!',
  messageEn: 'Get 30% off on all React templates until end of week!',
  link: '/templates?category=react&discount=30',
  linkText: 'عرض التفاصيل',
  linkTextEn: 'View Details',
  isActive: true,
  emoji: '🔥'
}

// Alternative Info Announcement (switch SITE_ANNOUNCEMENT.type to 'info' to use)
export const INFO_ANNOUNCEMENT: SiteAnnouncement = {
  type: 'info',
  message: 'مرحباً بكم في منصة نينجا تمبليتس - أقوى القوالب البرمجية',
  messageEn: 'Welcome to NinjaTemplates - The most powerful programming templates',
  isActive: true,
  emoji: '🌐'
}

// Main Navigation Configuration
export const MAIN_NAVIGATION: NavigationItem[] = [
  {
    name: 'مواقع الويب',
    nameEn: 'Websites',
    href: '/websites',
    icon: 'globe',
    hasDropdown: true,
    dropdownItems: [
      {
        name: 'مواقع الشركات',
        nameEn: 'Corporate Sites',
        href: '/websites/corporate',
        icon: 'globe',
        description: 'مواقع احترافية للشركات والمؤسسات'
      },
      {
        name: 'التجارة الإلكترونية',
        nameEn: 'E-commerce',
        href: '/websites/ecommerce',
        icon: 'shopping-bag',
        description: 'متاجر إلكترونية متكاملة'
      },
      {
        name: 'المدونات',
        nameEn: 'Blogs',
        href: '/websites/blogs',
        icon: 'newspaper',
        description: 'قوالب مدونات عصرية'
      },
      {
        name: 'المنتديات',
        nameEn: 'Forums',
        href: '/websites/forums',
        icon: 'grid-3x3',
        description: 'منتديات تفاعلية'
      }
    ]
  },
  {
    name: 'التطبيقات',
    nameEn: 'Applications',
    href: '/applications',
    icon: 'smartphone',
    hasDropdown: true,
    dropdownItems: [
      {
        name: 'تطبيقات الجوال',
        nameEn: 'Mobile Apps',
        href: '/applications/mobile',
        icon: 'smartphone',
        description: 'تطبيقات iOS و Android'
      },
      {
        name: 'تطبيقات سطح المكتب',
        nameEn: 'Desktop Apps',
        href: '/applications/desktop',
        icon: 'grid-3x3',
        description: 'تطبيقات Windows و Mac'
      },
      {
        name: 'تطبيقات الويب التقدمية',
        nameEn: 'Progressive Web Apps',
        href: '/applications/pwa',
        icon: 'globe',
        description: 'PWA حديثة وسريعة'
      }
    ]
  },
  {
    name: 'التصميم',
    nameEn: 'Design',
    href: '/design',
    icon: 'palette',
    hasDropdown: true,
    dropdownItems: [
      {
        name: 'تصميم UI/UX',
        nameEn: 'UI/UX Design',
        href: '/design/ui-ux',
        icon: 'palette',
        description: 'واجهات مستخدم احترافية'
      },
      {
        name: 'تصميم الشعارات',
        nameEn: 'Logo Design',
        href: '/design/logos',
        icon: 'grid-3x3',
        description: 'شعارات إبداعية ومميزة'
      },
      {
        name: 'التصميم الجرافيكي',
        nameEn: 'Graphic Design',
        href: '/design/graphics',
        icon: 'palette',
        description: 'تصاميم جرافيكية متنوعة'
      }
    ]
  },
  {
    name: 'الخدمات',
    nameEn: 'Services',
    href: '/services',
    icon: 'headphones',
    hasDropdown: true,
    dropdownItems: [
      {
        name: 'الاستشارات التقنية',
        nameEn: 'Technical Consulting',
        href: '/services/consulting',
        icon: 'headphones',
        description: 'استشارات تقنية متخصصة'
      },
      {
        name: 'الدعم الفني',
        nameEn: 'Technical Support',
        href: '/services/support',
        icon: 'help-circle',
        description: 'دعم فني على مدار الساعة'
      },
      {
        name: 'الصيانة',
        nameEn: 'Maintenance',
        href: '/services/maintenance',
        icon: 'grid-3x3',
        description: 'صيانة وتحديث المشاريع'
      }
    ]
  }
]

// User Authentication Menu Items
export const AUTH_MENU_ITEMS = {
  login: {
    name: 'تسجيل الدخول',
    nameEn: 'Login',
    href: '/auth/login'
  },
  register: {
    name: 'إنشاء حساب',
    nameEn: 'Register',
    href: '/auth/register'
  },
  profile: {
    name: 'الملف الشخصي',
    nameEn: 'Profile',
    href: '/profile'
  },
  orders: {
    name: 'طلباتي',
    nameEn: 'My Orders',
    href: '/orders'
  },
  favorites: {
    name: 'المفضلة',
    nameEn: 'Favorites',
    href: '/favorites'
  },
  settings: {
    name: 'الإعدادات',
    nameEn: 'Settings',
    href: '/settings'
  },
  logout: {
    name: 'تسجيل الخروج',
    nameEn: 'Logout',
    href: '/auth/logout'
  }
}

// TopBar Dismissal Configuration
export const TOPBAR_CONFIG = {
  dismissalKey: 'ninja-topbar-dismissed',
  dismissalDuration: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  animationDuration: 300 // Animation duration in milliseconds
}

// Language Configuration
export const LANGUAGE_CONFIG = {
  default: 'ar',
  supported: ['ar', 'en'],
  labels: {
    ar: 'العربية',
    en: 'English'
  }
}

// Search Configuration
export const SEARCH_CONFIG = {
  placeholder: 'ابحث عن القوالب، المقالات، والمزيد...',
  placeholderEn: 'Search templates, articles, and more...',
  recentSearches: 'عمليات البحث الأخيرة',
  recentSearchesEn: 'Recent Searches',
  popularSearches: [
    'React',
    'Next.js',
    'تطبيقات الجوال',
    'لوحة تحكم',
    'متجر إلكتروني',
    'واجهات مستخدم'
  ]
}

// Utility function to get current announcement
export const getCurrentAnnouncement = (): SiteAnnouncement | null => {
  if (!SITE_ANNOUNCEMENT.isActive) return null
  return SITE_ANNOUNCEMENT
}

// Utility function to check if topbar should be shown
export const shouldShowTopBar = (): boolean => {
  if (typeof window === 'undefined') return true // SSR
  
  const announcement = getCurrentAnnouncement()
  if (!announcement) return false
  
  const dismissedData = localStorage.getItem(TOPBAR_CONFIG.dismissalKey)
  if (!dismissedData) return true
  
  try {
    const { timestamp } = JSON.parse(dismissedData)
    const now = Date.now()
    const timeDiff = now - timestamp
    
    return timeDiff > TOPBAR_CONFIG.dismissalDuration
  } catch {
    return true
  }
}

// Utility function to dismiss topbar
export const dismissTopBar = (): void => {
  if (typeof window === 'undefined') return
  
  const dismissalData = {
    timestamp: Date.now(),
    type: SITE_ANNOUNCEMENT.type
  }
  
  localStorage.setItem(TOPBAR_CONFIG.dismissalKey, JSON.stringify(dismissalData))
}
