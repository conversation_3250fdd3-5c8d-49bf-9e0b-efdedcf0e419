"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Mail, Lock } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthRedirect, useAuthForm } from '@/lib/auth/hooks'
import { loginSchema, type LoginFormData } from '@/lib/auth/types'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import GuestRoute from '@/components/auth/GuestRoute'
import { Checkbox } from '@/components/ui/checkbox'

export default function SimpleLoginPage() {
  const { signIn } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [rememberMe, setRememberMe] = useState(false)

  useAuthRedirect()

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    clearErrors()
    
    await handleSubmit(
      async () => {
        const result = await signIn(data.email, data.password)
        return result
      },
      () => {
        toast.success('تم تسجيل الدخول بنجاح')
      }
    )
  }

  return (
    <GuestRoute>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 arabic-heading">
              تسجيل الدخول
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600 arabic-text">
              مرحباً بعودتك
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-8">
            <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
              <AuthError error={errors.general} onDismiss={clearErrors} />

              <AuthInput
                {...register('email')}
                label="البريد الإلكتروني"
                type="email"
                icon={<Mail className="w-5 h-5" />}
                error={formErrors.email?.message}
                placeholder="أدخل بريدك الإلكتروني"
                required
              />

              <AuthInput
                {...register('password')}
                label="كلمة المرور"
                type="password"
                icon={<Lock className="w-5 h-5" />}
                error={formErrors.password?.message}
                placeholder="أدخل كلمة المرور"
                showPasswordToggle
                required
              />

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Checkbox
                    id="rememberMe"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(checked === true)}
                  />
                  <label
                    htmlFor="rememberMe"
                    className="text-sm text-gray-700 arabic-text cursor-pointer"
                  >
                    تذكرني
                  </label>
                </div>

                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-[#064635] hover:text-[#053d2f] arabic-text transition-colors"
                >
                  نسيت كلمة المرور؟
                </Link>
              </div>

              <AuthButton
                type="submit"
                loading={isSubmitting}
                loadingText="جاري تسجيل الدخول..."
              >
                تسجيل الدخول
              </AuthButton>

              <div className="text-center">
                <p className="text-gray-600 arabic-text">
                  ليس لديك حساب؟{' '}
                  <Link
                    href="/auth/register"
                    className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
                  >
                    إنشاء حساب جديد
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </GuestRoute>
  )
}
