"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Lock, Shield, CheckCircle, Eye, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthForm } from '@/lib/auth/hooks'
import { resetPasswordSchema, type ResetPasswordFormData } from '@/lib/auth/types'
import TwoColumnAuthLayout from '@/components/auth/TwoColumnAuthLayout'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import AuthSuccess from '@/components/auth/AuthSuccess'
import GuestRoute from '@/components/auth/GuestRoute'
import PasswordStrength from '@/components/auth/PasswordStrength'

export default function ResetPasswordPage() {
  const { updatePassword } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
    watch,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const password = watch('password')

  // Check if we have valid reset token
  useEffect(() => {
    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')
    const type = searchParams.get('type')

    // Check for required parameters from Supabase password reset flow
    if (!accessToken || !refreshToken || type !== 'recovery') {
      setIsValidToken(false)
      console.error('Invalid reset token parameters:', { accessToken: !!accessToken, refreshToken: !!refreshToken, type })
    } else {
      setIsValidToken(true)
    }
  }, [searchParams])

  const onSubmit = async (data: ResetPasswordFormData) => {
    clearErrors()
    setSuccessMessage(null)

    await handleSubmit(
      async () => {
        const result = await updatePassword(data.password)
        return result
      },
      () => {
        setSuccessMessage('تم تغيير كلمة المرور بنجاح')
        toast.success('تم تغيير كلمة المرور بنجاح')

        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login?message=password-reset-success')
        }, 3000)
      }
    )
  }



  // Show error if invalid token
  if (isValidToken === false) {
    return (
      <GuestRoute>
        <TwoColumnAuthLayout
          title="رابط غير صالح"
          subtitle="الرابط منتهي الصلاحية أو غير صحيح"
          rightContent="forgot-password"
        >
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 arabic-heading">
                رابط إعادة تعيين كلمة المرور غير صالح
              </h3>
              <p className="text-gray-600 arabic-text">
                الرابط قد يكون منتهي الصلاحية أو تم استخدامه من قبل
              </p>
            </div>

            <div className="space-y-3">
              <Link href="/auth/forgot-password">
                <AuthButton variant="primary" className="w-full">
                  طلب رابط جديد
                </AuthButton>
              </Link>

              <Link href="/auth/login">
                <AuthButton variant="ghost" className="w-full">
                  العودة لتسجيل الدخول
                </AuthButton>
              </Link>
            </div>
          </div>
        </TwoColumnAuthLayout>
      </GuestRoute>
    )
  }

  // Show loading while checking token
  if (isValidToken === null) {
    return (
      <GuestRoute>
        <TwoColumnAuthLayout
          title="جاري التحقق..."
          subtitle="يرجى الانتظار"
          rightContent="forgot-password"
        >
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#064635] mx-auto"></div>
            <p className="mt-4 text-gray-600 arabic-text">جاري التحقق من صحة الرابط...</p>
          </div>
        </TwoColumnAuthLayout>
      </GuestRoute>
    )
  }

  return (
    <GuestRoute>
      <TwoColumnAuthLayout
        title="إعادة تعيين كلمة المرور"
        subtitle="قم بتعيين كلمة مرور جديدة بأمان"
        rightContent="reset-password"
      >
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          <AuthError error={errors.general} onDismiss={clearErrors} />
          <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

          <div className="text-center mb-6">
            <p className="text-gray-600 arabic-text">
              اختر كلمة مرور قوية وفريدة لحماية حسابك
            </p>
          </div>

          <AuthInput
            {...register('password')}
            label="كلمة المرور الجديدة"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.password?.message}
            placeholder="أدخل كلمة مرور قوية"
            showPasswordToggle
            required
          />

          {password && (
            <PasswordStrength password={password} />
          )}

          <AuthInput
            {...register('confirmPassword')}
            label="تأكيد كلمة المرور الجديدة"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.confirmPassword?.message}
            placeholder="أعد إدخال كلمة المرور"
            showPasswordToggle
            required
          />

          <AuthButton
            type="submit"
            loading={isSubmitting}
            loadingText="جاري تحديث كلمة المرور..."
          >
            تحديث كلمة المرور
          </AuthButton>

          <div className="text-center">
            <p className="text-gray-600 arabic-text">
              تذكرت كلمة المرور؟{' '}
              <Link
                href="/auth/login"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </form>
      </TwoColumnAuthLayout>
    </GuestRoute>
  )
}
