"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Lock, Shield, CheckCircle, Eye, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthForm } from '@/lib/auth/hooks'
import { resetPasswordSchema, type ResetPasswordFormData } from '@/lib/auth/types'
import AuthLayout from '@/components/auth/AuthLayout'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import AuthSuccess from '@/components/auth/AuthSuccess'
import GuestRoute from '@/components/auth/GuestRoute'
import PasswordStrength from '@/components/auth/PasswordStrength'

export default function ResetPasswordPage() {
  const { updatePassword } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
    watch,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const password = watch('password')

  // Check if we have valid reset token
  useEffect(() => {
    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')
    
    if (!accessToken || !refreshToken) {
      setIsValidToken(false)
    } else {
      setIsValidToken(true)
    }
  }, [searchParams])

  const onSubmit = async (data: ResetPasswordFormData) => {
    clearErrors()
    setSuccessMessage(null)
    
    await handleSubmit(
      async () => {
        const result = await updatePassword(data.password)
        return result
      },
      () => {
        setSuccessMessage('تم تحديث كلمة المرور بنجاح')
        toast.success('تم تحديث كلمة المرور بنجاح')
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login')
        }, 3000)
      }
    )
  }

  const rightContent = (
    <div className="space-y-8">
      <div>
        <h2 className="text-4xl font-bold mb-4 arabic-heading">
          قم بتعيين كلمة مرور جديدة بأمان
        </h2>
        <p className="text-xl text-white/90 arabic-text leading-relaxed">
          اختر كلمة مرور قوية وفريدة لحماية حسابك من التهديدات
        </p>
      </div>

      <div className="space-y-6">
        <h3 className="text-2xl font-semibold arabic-heading">
          أمان متقدم
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">تشفير عالي المستوى</h4>
              <p className="text-white/80 arabic-text">كلمة المرور محمية بتشفير AES-256</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">حماية من التهديدات</h4>
              <p className="text-white/80 arabic-text">نظام متقدم لكشف المحاولات المشبوهة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">مراقبة أمنية مستمرة</h4>
              <p className="text-white/80 arabic-text">مراقبة على مدار الساعة لحماية حسابك</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">امتثال لمعايير الأمان العالمية</h4>
              <p className="text-white/80 arabic-text">نتبع أفضل الممارسات الأمنية الدولية</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white/10 rounded-lg p-6">
        <h4 className="font-semibold mb-2 arabic-text">نصائح لكلمة مرور قوية:</h4>
        <ul className="space-y-2 text-sm text-white/80 arabic-text">
          <li>• استخدم 12 حرف أو أكثر</li>
          <li>• امزج بين الأحرف الكبيرة والصغيرة</li>
          <li>• أضف أرقام ورموز خاصة</li>
          <li>• تجنب المعلومات الشخصية</li>
          <li>• لا تستخدم كلمات مرور مكررة</li>
        </ul>
      </div>
    </div>
  )

  // Show error if invalid token
  if (isValidToken === false) {
    return (
      <GuestRoute>
        <AuthLayout
          title="رابط غير صالح"
          subtitle="الرابط منتهي الصلاحية أو غير صحيح"
          rightContent={rightContent}
        >
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 arabic-heading">
                رابط إعادة تعيين كلمة المرور غير صالح
              </h3>
              <p className="text-gray-600 arabic-text">
                الرابط قد يكون منتهي الصلاحية أو تم استخدامه من قبل
              </p>
            </div>

            <div className="space-y-3">
              <AuthButton asChild variant="primary">
                <Link href="/auth/forgot-password">
                  طلب رابط جديد
                </Link>
              </AuthButton>
              
              <AuthButton asChild variant="ghost">
                <Link href="/auth/login">
                  العودة لتسجيل الدخول
                </Link>
              </AuthButton>
            </div>
          </div>
        </AuthLayout>
      </GuestRoute>
    )
  }

  // Show loading while checking token
  if (isValidToken === null) {
    return (
      <GuestRoute>
        <AuthLayout
          title="جاري التحقق..."
          subtitle="يرجى الانتظار"
          rightContent={rightContent}
        >
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#064635] mx-auto"></div>
            <p className="mt-4 text-gray-600 arabic-text">جاري التحقق من صحة الرابط...</p>
          </div>
        </AuthLayout>
      </GuestRoute>
    )
  }

  return (
    <GuestRoute>
      <AuthLayout
        title="إعادة تعيين كلمة المرور"
        subtitle="قم بتعيين كلمة مرور جديدة بأمان"
        rightContent={rightContent}
      >
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          <AuthError error={errors.general} onDismiss={clearErrors} />
          <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

          <div className="text-center mb-6">
            <p className="text-gray-600 arabic-text">
              اختر كلمة مرور قوية وفريدة لحماية حسابك
            </p>
          </div>

          <AuthInput
            {...register('password')}
            label="كلمة المرور الجديدة"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.password?.message}
            placeholder="أدخل كلمة مرور قوية"
            showPasswordToggle
            required
          />

          {password && (
            <PasswordStrength password={password} />
          )}

          <AuthInput
            {...register('confirmPassword')}
            label="تأكيد كلمة المرور الجديدة"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.confirmPassword?.message}
            placeholder="أعد إدخال كلمة المرور"
            showPasswordToggle
            required
          />

          <AuthButton
            type="submit"
            loading={isSubmitting}
            loadingText="جاري تحديث كلمة المرور..."
          >
            تحديث كلمة المرور
          </AuthButton>

          <div className="text-center">
            <p className="text-gray-600 arabic-text">
              تذكرت كلمة المرور؟{' '}
              <Link
                href="/auth/login"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </form>
      </AuthLayout>
    </GuestRoute>
  )
}
