"use client"

import React, { useState, forwardRef } from 'react'
import { Eye, EyeOff, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'

interface AuthInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  error?: string
  showPasswordToggle?: boolean
  icon?: React.ReactNode
  description?: string
}

const AuthInput = forwardRef<HTMLInputElement, AuthInputProps>(
  ({ 
    label, 
    error, 
    showPasswordToggle = false, 
    icon, 
    description,
    className,
    type,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)

    const inputType = showPasswordToggle 
      ? (showPassword ? 'text' : 'password')
      : type

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={props.id} 
          className="text-sm font-medium text-gray-700 arabic-text"
        >
          {label}
          {props.required && <span className="text-red-500 mr-1 rtl:mr-0 rtl:ml-1">*</span>}
        </Label>
        
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              {icon}
            </div>
          )}
          
          {/* Input */}
          <Input
            ref={ref}
            type={inputType}
            className={cn(
              "w-full h-12 px-4 text-base arabic-text transition-all duration-200",
              icon && "pl-10 rtl:pl-4 rtl:pr-10",
              showPasswordToggle && "pr-12 rtl:pr-4 rtl:pl-12",
              error && "border-red-500 focus:border-red-500 focus:ring-red-500",
              !error && isFocused && "border-[#064635] ring-2 ring-[#064635]/20",
              className
            )}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...props}
          />
          
          {/* Password Toggle */}
          {showPasswordToggle && (
            <button
              type="button"
              className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          )}
        </div>
        
        {/* Description */}
        {description && !error && (
          <p className="text-sm text-gray-500 arabic-text">
            {description}
          </p>
        )}
        
        {/* Error Message */}
        {error && (
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-red-600">
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            <p className="text-sm arabic-text">{error}</p>
          </div>
        )}
      </div>
    )
  }
)

AuthInput.displayName = 'AuthInput'

export default AuthInput
