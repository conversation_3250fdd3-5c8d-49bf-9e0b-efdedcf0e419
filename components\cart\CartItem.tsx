"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Minus, Plus, Trash2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useCartStore, CartItem as CartItemType } from '@/stores/cartStore'
import { useI18n } from '@/contexts/I18nContext'
import cartTranslations from '@/lib/translations/cart.json'

interface CartItemProps {
  item: CartItemType
}

export default function CartItem({ item }: CartItemProps) {
  const { locale } = useI18n()
  const t = cartTranslations[locale as keyof typeof cartTranslations]
  const { updateQuantity, removeItem } = useCartStore()
  const [isRemoving, setIsRemoving] = useState(false)

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 99) {
      updateQuantity(item.id, newQuantity)
    }
  }

  const handleRemove = () => {
    setIsRemoving(true)
    setTimeout(() => {
      removeItem(item.id)
    }, 200)
  }

  const formatPrice = (price: number) => {
    return locale === 'ar' ? `${price} ${t.currency}` : `${t.currency} ${price}`
  }

  const displayTitle = locale === 'ar' ? item.titleAr : item.title

  return (
    <motion.div
      layout
      initial={{ opacity: 1, x: 0 }}
      animate={{ 
        opacity: isRemoving ? 0 : 1,
        x: isRemoving ? (locale === 'ar' ? 100 : -100) : 0
      }}
      exit={{ opacity: 0, x: locale === 'ar' ? 100 : -100 }}
      transition={{ duration: 0.2 }}
      className="flex items-start gap-4 p-4 border-b border-gray-100 last:border-b-0"
    >
      {/* Template Image */}
      <div className="flex-shrink-0">
        <div className="w-20 h-15 bg-gray-100 rounded-lg overflow-hidden">
          <Image
            src={item.image}
            alt={displayTitle}
            width={80}
            height={60}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Item Details */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-gray-900 text-sm arabic-text line-clamp-2 mb-1">
          {displayTitle}
        </h4>
        
        {/* Technology Tags */}
        <div className="flex flex-wrap gap-1 mb-2">
          {item.tags.slice(0, 2).map((tag, index) => (
            <span
              key={index}
              className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Price and Quantity Controls */}
        <div className="flex items-center justify-between">
          <div className="text-saudi-primary font-bold text-sm">
            {formatPrice(item.price * item.quantity)}
          </div>

          {/* Quantity Controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(item.quantity - 1)}
              disabled={item.quantity <= 1}
              className="w-8 h-8 p-0"
            >
              <Minus className="w-3 h-3" />
            </Button>

            <Input
              type="number"
              min="1"
              max="99"
              value={item.quantity}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 1
                handleQuantityChange(value)
              }}
              className="w-12 h-8 text-center text-sm p-0 border-gray-200"
            />

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(item.quantity + 1)}
              disabled={item.quantity >= 99}
              className="w-8 h-8 p-0"
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Remove Button */}
      <div className="flex-shrink-0">
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-8 h-8 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2 arabic-heading">
                <AlertCircle className="w-5 h-5 text-red-500" />
                {t.remove}
              </AlertDialogTitle>
              <AlertDialogDescription className="arabic-text">
                {t.removeConfirm}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-2">
              <AlertDialogCancel className="arabic-text">إلغاء</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleRemove}
                className="bg-red-500 hover:bg-red-600 arabic-text"
              >
                {t.remove}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </motion.div>
  )
}
