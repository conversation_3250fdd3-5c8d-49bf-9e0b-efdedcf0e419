import type React from 'react'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { I18nProvider } from '@/contexts/I18nContext'

// English font configuration
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'NinjaTemplates - نينجا تمبليتس | أقوى القوالب البرمجية الجاهزة',
  description:
    'اكتشف مجموعة متنوعة من القوالب عالية الجودة لتطوير مشاريعك بسرعة واحترافية. قوالب تطبيقات، واجهات مستخدم، ومواقع ويب جاهزة للاستخدام.',
  keywords: [
    'قوالب برمجية',
    'تطبيقات جاهزة',
    'واجهات مستخدم',
    'قوالب ويب',
    'Templates',
    'UI/UX',
    'Web Development',
  ],
  authors: [{ name: 'NinjaTemplates' }],
  creator: 'NinjaTemplates',
  generator: 'Next.js',
  openGraph: {
    title: 'NinjaTemplates - نينجا تمبليتس',
    description: 'أقوى القوالب البرمجية الجاهزة للمطورين العرب',
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NinjaTemplates - نينجا تمبليتس',
    description: 'أقوى القوالب البرمجية الجاهزة للمطورين العرب',
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100..900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body
        className={`${inter.variable} antialiased`}
        style={{ fontFamily: 'Vazirmatn, sans-serif', direction: 'rtl' }}
      >
        <I18nProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <div className="min-h-screen bg-white">
              <main className="relative flex min-h-screen flex-col">{children}</main>
            </div>
          </ThemeProvider>
        </I18nProvider>
      </body>
    </html>
  )
}
