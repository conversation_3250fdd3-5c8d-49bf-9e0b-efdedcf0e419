'use client'

import React from 'react'
import Link from 'next/link'
import { ArrowRight, Star, CheckCircle, Users, Zap, Shield } from 'lucide-react'

interface TwoColumnAuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showBackToHome?: boolean
  rightContent?: 'login' | 'register' | 'forgot-password'
}

const TwoColumnAuthLayout: React.FC<TwoColumnAuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showBackToHome = true,
  rightContent = 'login'
}) => {
  const getRightContent = () => {
    switch (rightContent) {
      case 'register':
        return (
          <div className="text-white space-y-8">
            <div>
              <h2 className="text-3xl font-bold mb-4 arabic-text">
                مرحباً بك في NinjaTemplates
              </h2>
              <p className="text-lg opacity-90 arabic-text">
                منصة القوالب البرمجية المتقدمة للمطورين والمصممين
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">فرص بيع مربحة للمطورين</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">مجتمع نشط من المطورين</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">لوحة تحكم متقدمة للبائعين</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">دعم فني متخصص</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">عمولات تنافسية</span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="text-sm arabic-text">أكثر من 5,000 مطور راضي</span>
              </div>
              <p className="text-sm opacity-90 arabic-text">
                "أفضل منصة لبيع القوالب البرمجية"
              </p>
            </div>
          </div>
        )
      
      case 'login':
        return (
          <div className="text-white space-y-8">
            <div>
              <h2 className="text-3xl font-bold mb-4 arabic-text">
                مرحباً بك في NinjaTemplates
              </h2>
              <p className="text-lg opacity-90 arabic-text">
                منصة القوالب البرمجية الرائدة في المنطقة
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Shield className="w-6 h-6 text-green-300" />
                <span className="arabic-text">ضمان جودة القوالب</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Users className="w-6 h-6 text-green-300" />
                <span className="arabic-text">مجتمع من المطورين المحترفين</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Zap className="w-6 h-6 text-green-300" />
                <span className="arabic-text">تحميل فوري للقوالب</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">لوحة تحكم متقدمة</span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="text-sm arabic-text">أكثر من 10,000 قالب عالي الجودة</span>
              </div>
              <p className="text-sm opacity-90 arabic-text">
                "أفضل منصة للقوالب البرمجية"
              </p>
            </div>
          </div>
        )
      
      default:
        return (
          <div className="text-white space-y-8">
            <div>
              <h2 className="text-3xl font-bold mb-4 arabic-text">
                مرحباً بك في NinjaTemplates
              </h2>
              <p className="text-lg opacity-90 arabic-text">
                نحن هنا لمساعدتك في استعادة حسابك بأمان
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Shield className="w-6 h-6 text-green-300" />
                <span className="arabic-text">حماية متقدمة للبيانات</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="w-6 h-6 text-green-300" />
                <span className="arabic-text">استعادة آمنة وسريعة</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Users className="w-6 h-6 text-green-300" />
                <span className="arabic-text">دعم فني متخصص</span>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex" dir="rtl">
      {/* Left Column - Form (40% on desktop) */}
      <div className="w-full lg:w-2/5 flex flex-col justify-center px-6 py-12 lg:px-8 order-2 lg:order-1">
        <div className="mx-auto w-full max-w-sm">
          {showBackToHome && (
            <div className="mb-8">
              <Link
                href="/"
                className="inline-flex items-center text-sm text-gray-600 hover:text-[#064635] transition-colors duration-200 arabic-text"
              >
                <ArrowRight className="w-4 h-4 ml-1 rtl:ml-0 rtl:mr-1" />
                العودة للرئيسية
              </Link>
            </div>
          )}

          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 arabic-text">
              {title}
            </h1>
            <p className="mt-2 text-sm text-gray-600 arabic-text">
              {subtitle}
            </p>
          </div>

          {children}
        </div>
      </div>

      {/* Right Column - Branding (60% on desktop) */}
      <div className="hidden lg:flex lg:w-3/5 bg-gradient-to-br from-[#064635] via-[#0a5a42] to-[#0e6b4f] items-center justify-center p-12 order-1 lg:order-2">
        <div className="max-w-md">
          {getRightContent()}
        </div>
      </div>

      {/* Mobile branding section */}
      <div className="lg:hidden w-full bg-gradient-to-r from-[#064635] to-[#0a5a42] p-6 order-1">
        <div className="text-center text-white">
          <h2 className="text-xl font-bold mb-2 arabic-text">
            مرحباً بك في NinjaTemplates
          </h2>
          <p className="text-sm opacity-90 arabic-text">
            منصة القوالب البرمجية المتقدمة
          </p>
        </div>
      </div>
    </div>
  )
}

export default TwoColumnAuthLayout
