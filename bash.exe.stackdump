Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAD6F30000 ntdll.dll
7FFAD5990000 KERNEL32.DLL
7FFAD48A0000 KERNELBASE.dll
7FFAD4F40000 USER32.dll
7FFAD44C0000 win32u.dll
7FFAD58E0000 GDI32.dll
7FFAD4780000 gdi32full.dll
7FFAD4420000 msvcp_win.dll
7FFAD4240000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAD6C10000 advapi32.dll
7FFAD4CD0000 msvcrt.dll
7FFAD5240000 sechost.dll
7FFAD4570000 bcrypt.dll
7FFAD6DD0000 RPCRT4.dll
7FFAD3950000 CRYPTBASE.DLL
7FFAD44F0000 bcryptPrimitives.dll
7FFAD5860000 IMM32.DLL
