'use client'

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Globe, ArrowLeft, ArrowRight } from 'lucide-react'
import { useI18n } from '@/contexts/I18nContext'
import { LanguageSelector } from '@/components/language-selector'

export default function I18nTestPage() {
  const { locale, t } = useI18n()
  const isRTL = locale === 'ar'

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-white p-4 rounded-full shadow-lg">
              <Globe className="h-12 w-12 text-blue-600" />
            </div>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 mb-4">{t.common.test.title}</h1>

          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">{t.common.test.subtitle}</p>

          {/* Language Selector */}
          <div className="flex justify-center mb-8">
            <div className="bg-white p-4 rounded-lg shadow-md">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-700">{t.common.language}:</span>
                <LanguageSelector />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid gap-8 md:grid-cols-2">
          {/* Current Language Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {t.common.test.currentLanguage}
              </CardTitle>
              <CardDescription>{t.common.test.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{locale === 'ar' ? 'العربية' : 'English'}</Badge>
                  <span className="text-sm text-gray-600">
                    {locale === 'ar' ? 'Right-to-Left' : 'Left-to-Right'}
                  </span>
                </div>

                <p className="text-sm text-gray-700">{t.common.test.switchPrompt}</p>
              </div>
            </CardContent>
          </Card>

          {/* Features List */}
          <Card>
            <CardHeader>
              <CardTitle>{t.common.test.features.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{t.common.test.features.languageSwitch}</span>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{t.common.test.features.persistence}</span>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{t.common.test.features.rtl}</span>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{t.common.test.features.ltr}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sample Content Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>{locale === 'ar' ? 'محتوى تجريبي' : 'Sample Content'}</CardTitle>
            <CardDescription>
              {locale === 'ar'
                ? 'هذا المحتوى يتغير حسب اللغة المختارة'
                : 'This content changes based on the selected language'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h3 className="font-semibold mb-2">
                  {locale === 'ar' ? 'النص الأساسي' : 'Basic Text'}
                </h3>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {locale === 'ar'
                    ? 'هذا نص تجريبي باللغة العربية. يمكنك ملاحظة أن النص يظهر من اليمين إلى اليسار، وأن التخطيط العام للصفحة يتكيف مع اتجاه الكتابة.'
                    : 'This is sample text in English. You can notice that the text flows from left to right, and the overall page layout adapts to the writing direction.'}
                </p>
              </div>

              <div>
                <h3 className="font-semibold mb-2">
                  {locale === 'ar' ? 'الأرقام والتواريخ' : 'Numbers and Dates'}
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>{locale === 'ar' ? 'التاريخ:' : 'Date:'}</span>
                    <span>
                      {new Date().toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>{locale === 'ar' ? 'الرقم:' : 'Number:'}</span>
                    <span>{(12345.67).toLocaleString(locale === 'ar' ? 'ar-SA' : 'en-US')}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <Link href="/">
            <Button variant="outline" className="gap-2">
              {isRTL ? <ArrowRight className="h-4 w-4" /> : <ArrowLeft className="h-4 w-4" />}
              {t.common.test.backToHome}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
