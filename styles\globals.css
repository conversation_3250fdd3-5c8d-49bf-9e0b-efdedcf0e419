@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* === SEMANTIC DESIGN TOKENS === */

    /* Surface Colors */
    --surface-primary: #ffffff;
    --surface-secondary: #f8fafc;
    --surface-tertiary: #f1f5f9;
    --surface-accent: #1B7B3A;
    --surface-accent-hover: #0F5A2A;
    --surface-accent-light: #E8F5E8;
    --surface-muted: #f9fafb;
    --surface-overlay: rgba(0, 0, 0, 0.5);

    /* Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --text-accent: #1B7B3A;
    --text-accent-hover: #0F5A2A;
    --text-inverse: #ffffff;
    --text-muted: #64748b;

    /* Border Colors */
    --border-default: #e5e7eb;
    --border-secondary: #d1d5db;
    --border-accent: #1B7B3A;
    --border-muted: #f3f4f6;

    /* State Colors */
    --state-success: #10b981;
    --state-success-light: #d1fae5;
    --state-warning: #f59e0b;
    --state-warning-light: #fef3c7;
    --state-error: #ef4444;
    --state-error-light: #fee2e2;
    --state-info: #3b82f6;
    --state-info-light: #dbeafe;

    /* Badge Colors */
    --badge-featured-bg: #fef3c7;
    --badge-featured-text: #b45309;
    --badge-featured-border: #fbbf24;
    --badge-bestseller-bg: #fed7aa;
    --badge-bestseller-text: #c2410c;
    --badge-bestseller-border: #fb923c;
    --badge-new-bg: #d1fae5;
    --badge-new-text: #065f46;
    --badge-new-border: #34d399;
    --badge-default-bg: #f3f4f6;
    --badge-default-text: #374151;
    --badge-default-border: #d1d5db;

    /* Interactive States */
    --interactive-hover: #f9fafb;
    --interactive-active: #f3f4f6;
    --interactive-focus: #1B7B3A;
    --interactive-disabled: #f3f4f6;
    --interactive-disabled-text: #9ca3af;

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #1B7B3A 0%, #2D8F47 100%);
    --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-accent: linear-gradient(135deg, #E8F5E8 0%, #C8E6C8 100%);

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-accent: 0 10px 15px -3px rgba(27, 123, 58, 0.1), 0 4px 6px -2px rgba(27, 123, 58, 0.05);

    /* === SHADCN/UI COMPATIBILITY === */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 142 76% 36%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 142 76% 36%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 142 76% 36%;
  }
  .dark {
    /* === DARK THEME SEMANTIC TOKENS === */

    /* Surface Colors */
    --surface-primary: #1f2937;
    --surface-secondary: #374151;
    --surface-tertiary: #4b5563;
    --surface-accent: #2D8F47;
    --surface-accent-hover: #1B7B3A;
    --surface-accent-light: #064635;
    --surface-muted: #111827;
    --surface-overlay: rgba(0, 0, 0, 0.8);

    /* Text Colors */
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --text-accent: #2D8F47;
    --text-accent-hover: #1B7B3A;
    --text-inverse: #1f2937;
    --text-muted: #6b7280;

    /* Border Colors */
    --border-default: #374151;
    --border-secondary: #4b5563;
    --border-accent: #2D8F47;
    --border-muted: #1f2937;

    /* State Colors */
    --state-success: #34d399;
    --state-success-light: #064e3b;
    --state-warning: #fbbf24;
    --state-warning-light: #451a03;
    --state-error: #f87171;
    --state-error-light: #450a0a;
    --state-info: #60a5fa;
    --state-info-light: #1e3a8a;

    /* Badge Colors */
    --badge-featured-bg: #451a03;
    --badge-featured-text: #fbbf24;
    --badge-featured-border: #92400e;
    --badge-bestseller-bg: #431407;
    --badge-bestseller-text: #fb923c;
    --badge-bestseller-border: #c2410c;
    --badge-new-bg: #064e3b;
    --badge-new-text: #34d399;
    --badge-new-border: #059669;
    --badge-default-bg: #374151;
    --badge-default-text: #d1d5db;
    --badge-default-border: #4b5563;

    /* Interactive States */
    --interactive-hover: #374151;
    --interactive-active: #4b5563;
    --interactive-focus: #2D8F47;
    --interactive-disabled: #374151;
    --interactive-disabled-text: #6b7280;

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #2D8F47 0%, #1B7B3A 100%);
    --gradient-secondary: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    --gradient-accent: linear-gradient(135deg, #064635 0%, #0f3d2a 100%);

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-accent: 0 10px 15px -3px rgba(45, 143, 71, 0.3), 0 4px 6px -2px rgba(45, 143, 71, 0.2);

    /* === SHADCN/UI DARK COMPATIBILITY === */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 142 76% 36%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 142 76% 36%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
