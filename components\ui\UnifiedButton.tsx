"use client"

import React from 'react'
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-600 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-[#064635] text-white hover:bg-[#053d2f] active:bg-[#042e24] shadow-sm hover:shadow-md",
        secondary: "border-2 border-[#064635] text-[#064635] bg-transparent hover:bg-[#064635] hover:text-white active:bg-[#053d2f]",
        outline: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400",
        ghost: "text-gray-700 hover:bg-gray-100 hover:text-[#064635]",
        icon: "text-gray-600 hover:text-[#064635] hover:bg-green-50 rounded-full",
        destructive: "bg-red-600 text-white hover:bg-red-700 active:bg-red-800",
        link: "text-[#064635] underline-offset-4 hover:underline p-0 h-auto"
      },
      size: {
        sm: "h-8 px-3 text-xs",
        default: "h-10 px-4 py-2",
        lg: "h-12 px-6 text-base",
        xl: "h-14 px-8 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12"
      },
      rounded: {
        default: "rounded-lg",
        full: "rounded-full",
        none: "rounded-none"
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      rounded: "default"
    },
  }
)

export interface UnifiedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const UnifiedButton = React.forwardRef<HTMLButtonElement, UnifiedButtonProps>(
  ({
    className,
    variant,
    size,
    rounded,
    asChild = false,
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props
  }, ref) => {
    // When asChild is true, we need to pass props to the child element
    // and only render the children without additional wrapper elements
    if (asChild) {
      // Validate that we have exactly one child when using asChild
      const childCount = React.Children.count(children)

      if (childCount !== 1) {
        console.error(`UnifiedButton with asChild prop expects exactly one child element, but received ${childCount} children:`, children)
        // Fallback to regular button rendering
        return (
          <button
            className={cn(buttonVariants({ variant, size, rounded, className }))}
            ref={ref}
            disabled={disabled || loading}
            {...props}
          >
            {children}
          </button>
        )
      }

      try {
        const child = React.Children.only(children)

        // Ensure the child is a valid React element
        if (!React.isValidElement(child)) {
          console.error('UnifiedButton asChild received invalid React element:', child)
          return (
            <button
              className={cn(buttonVariants({ variant, size, rounded, className }))}
              ref={ref}
              disabled={disabled || loading}
              {...props}
            >
              {children}
            </button>
          )
        }

        // Clone the child element and pass our props to it
        const childProps = child.props as any
        return React.cloneElement(child, {
          className: cn(buttonVariants({ variant, size, rounded }), className, childProps?.className),
          ref,
          disabled: disabled || loading,
          ...props,
          ...(childProps || {}), // Preserve original child props (this should come last to avoid overriding our props)
        })
      } catch (error) {
        console.error('Error in UnifiedButton asChild rendering:', error)
        // Fallback to regular button rendering
        return (
          <button
            className={cn(buttonVariants({ variant, size, rounded, className }))}
            ref={ref}
            disabled={disabled || loading}
            {...props}
          >
            {children}
          </button>
        )
      }
    }

    // Regular button rendering when asChild is false
    return (
      <button
        className={cn(buttonVariants({ variant, size, rounded, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="mr-2 rtl:mr-0 rtl:ml-2">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
          </div>
        )}
        {leftIcon && !loading && (
          <span className="mr-2 rtl:mr-0 rtl:ml-2">
            {leftIcon}
          </span>
        )}
        {children}
        {rightIcon && (
          <span className="ml-2 rtl:ml-0 rtl:mr-2">
            {rightIcon}
          </span>
        )}
      </button>
    )
  }
)

UnifiedButton.displayName = "UnifiedButton"

export { UnifiedButton, buttonVariants }

// Convenience components for common use cases
export const PrimaryButton = React.forwardRef<HTMLButtonElement, Omit<UnifiedButtonProps, 'variant'>>(
  (props, ref) => <UnifiedButton variant="primary" ref={ref} {...props} />
)

PrimaryButton.displayName = "PrimaryButton"

export const SecondaryButton = React.forwardRef<HTMLButtonElement, Omit<UnifiedButtonProps, 'variant'>>(
  (props, ref) => <UnifiedButton variant="secondary" ref={ref} {...props} />
)

SecondaryButton.displayName = "SecondaryButton"

export const IconButton = React.forwardRef<HTMLButtonElement, Omit<UnifiedButtonProps, 'variant'>>(
  ({ size = "icon", ...props }, ref) => <UnifiedButton variant="icon" size={size} ref={ref} {...props} />
)

IconButton.displayName = "IconButton"

export const GhostButton = React.forwardRef<HTMLButtonElement, Omit<UnifiedButtonProps, 'variant'>>(
  (props, ref) => <UnifiedButton variant="ghost" ref={ref} {...props} />
)

GhostButton.displayName = "GhostButton"

export const OutlineButton = React.forwardRef<HTMLButtonElement, Omit<UnifiedButtonProps, 'variant'>>(
  (props, ref) => <UnifiedButton variant="outline" ref={ref} {...props} />
)

OutlineButton.displayName = "OutlineButton"

// RTL-aware button with automatic icon positioning
export const RTLButton = React.forwardRef<HTMLButtonElement, UnifiedButtonProps & {
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right' | 'auto'
}>(
  ({ icon, iconPosition = 'auto', children, ...props }, ref) => {
    const isRTL = typeof document !== 'undefined' && document.documentElement.dir === 'rtl'

    let leftIcon, rightIcon

    if (icon) {
      if (iconPosition === 'auto') {
        // In RTL, icons typically go on the right for actions
        if (isRTL) {
          rightIcon = icon
        } else {
          leftIcon = icon
        }
      } else if (iconPosition === 'left') {
        leftIcon = icon
      } else {
        rightIcon = icon
      }
    }

    return (
      <UnifiedButton
        ref={ref}
        leftIcon={leftIcon}
        rightIcon={rightIcon}
        {...props}
      >
        {children}
      </UnifiedButton>
    )
  }
)

RTLButton.displayName = "RTLButton"
