/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // === SEMANTIC DESIGN TOKENS ===

        // Surface Colors
        'surface': {
          'primary': 'var(--surface-primary)',
          'secondary': 'var(--surface-secondary)',
          'tertiary': 'var(--surface-tertiary)',
          'accent': 'var(--surface-accent)',
          'accent-hover': 'var(--surface-accent-hover)',
          'accent-light': 'var(--surface-accent-light)',
          'muted': 'var(--surface-muted)',
          'overlay': 'var(--surface-overlay)',
        },

        // Text Colors
        'text': {
          'primary': 'var(--text-primary)',
          'secondary': 'var(--text-secondary)',
          'tertiary': 'var(--text-tertiary)',
          'accent': 'var(--text-accent)',
          'accent-hover': 'var(--text-accent-hover)',
          'inverse': 'var(--text-inverse)',
          'muted': 'var(--text-muted)',
        },

        // Border Colors
        'border': {
          'default': 'var(--border-default)',
          'secondary': 'var(--border-secondary)',
          'accent': 'var(--border-accent)',
          'muted': 'var(--border-muted)',
        },

        // State Colors
        'state': {
          'success': 'var(--state-success)',
          'success-light': 'var(--state-success-light)',
          'warning': 'var(--state-warning)',
          'warning-light': 'var(--state-warning-light)',
          'error': 'var(--state-error)',
          'error-light': 'var(--state-error-light)',
          'info': 'var(--state-info)',
          'info-light': 'var(--state-info-light)',
        },

        // Badge Colors
        'badge': {
          'featured-bg': 'var(--badge-featured-bg)',
          'featured-text': 'var(--badge-featured-text)',
          'featured-border': 'var(--badge-featured-border)',
          'bestseller-bg': 'var(--badge-bestseller-bg)',
          'bestseller-text': 'var(--badge-bestseller-text)',
          'bestseller-border': 'var(--badge-bestseller-border)',
          'new-bg': 'var(--badge-new-bg)',
          'new-text': 'var(--badge-new-text)',
          'new-border': 'var(--badge-new-border)',
          'default-bg': 'var(--badge-default-bg)',
          'default-text': 'var(--badge-default-text)',
          'default-border': 'var(--badge-default-border)',
        },

        // Interactive States
        'interactive': {
          'hover': 'var(--interactive-hover)',
          'active': 'var(--interactive-active)',
          'focus': 'var(--interactive-focus)',
          'disabled': 'var(--interactive-disabled)',
          'disabled-text': 'var(--interactive-disabled-text)',
        },

        // Legacy Saudi Colors (for backward compatibility)
        saudi: {
          primary: "#1B7B3A",
          secondary: "#0F5A2A",
          accent: "#2D8F47",
          light: "#E8F5E8",
          50: "#F0F9F0",
          100: "#E8F5E8",
          200: "#C8E6C8",
          300: "#A8D7A8",
          400: "#68B968",
          500: "#1B7B3A",
          600: "#186F34",
          700: "#155D2C",
          800: "#124B24",
          900: "#0F391C",
        },

        // shadcn/ui compatibility
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      fontFamily: {
        arabic: ['Cairo', 'Tajawal', 'system-ui', 'sans-serif'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        'semantic-sm': 'var(--shadow-sm)',
        'semantic-md': 'var(--shadow-md)',
        'semantic-lg': 'var(--shadow-lg)',
        'semantic-xl': 'var(--shadow-xl)',
        'semantic-accent': 'var(--shadow-accent)',
      },
      backgroundImage: {
        'gradient-primary': 'var(--gradient-primary)',
        'gradient-secondary': 'var(--gradient-secondary)',
        'gradient-accent': 'var(--gradient-accent)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
  ],
}
