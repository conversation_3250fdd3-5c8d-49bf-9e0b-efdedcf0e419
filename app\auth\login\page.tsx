"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Mail, Lock, CheckCircle, Download, Users, Headphones } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthRedirect, useAuthForm } from '@/lib/auth/hooks'
import { loginSchema, type LoginFormData } from '@/lib/auth/types'
import AuthLayout from '@/components/auth/AuthLayout'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import GuestRoute from '@/components/auth/GuestRoute'
import { Checkbox } from '@/components/ui/checkbox'

export default function LoginPage() {
  const { signIn } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [rememberMe, setRememberMe] = useState(false)

  useAuthRedirect()

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    clearErrors()
    
    await handleSubmit(
      async () => {
        const result = await signIn(data.email, data.password)
        return result
      },
      () => {
        toast.success('تم تسجيل الدخول بنجاح')
      }
    )
  }

  const rightContent = (
    <div className="space-y-8">
      <div>
        <h2 className="text-4xl font-bold mb-4 arabic-heading">
          مرحباً بعودتك!
        </h2>
        <p className="text-xl text-white/90 arabic-text leading-relaxed">
          سجل دخولك للوصول إلى أفضل القوالب البرمجية والاستمتاع بمزايا حصرية
        </p>
      </div>

      <div className="space-y-6">
        <h3 className="text-2xl font-semibold arabic-heading">
          استمتع بمزايا حصرية
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Download className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">وصول فوري لآلاف القوالب</h4>
              <p className="text-white/80 arabic-text">تحميل مباشر لجميع القوالب المتاحة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">تحديثات مجانية مدى الحياة</h4>
              <p className="text-white/80 arabic-text">احصل على آخر التحديثات والميزات</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Headphones className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">دعم فني متخصص</h4>
              <p className="text-white/80 arabic-text">فريق دعم متاح على مدار الساعة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">مجتمع من المطورين</h4>
              <p className="text-white/80 arabic-text">انضم لمجتمع من المطورين المحترفين</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <GuestRoute>
      <AuthLayout
        title="تسجيل الدخول"
        subtitle="مرحباً بعودتك"
        rightContent={rightContent}
      >
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          <AuthError error={errors.general} onDismiss={clearErrors} />

          <AuthInput
            {...register('email')}
            label="البريد الإلكتروني"
            type="email"
            icon={<Mail className="w-5 h-5" />}
            error={formErrors.email?.message}
            placeholder="أدخل بريدك الإلكتروني"
            required
          />

          <AuthInput
            {...register('password')}
            label="كلمة المرور"
            type="password"
            icon={<Lock className="w-5 h-5" />}
            error={formErrors.password?.message}
            placeholder="أدخل كلمة المرور"
            showPasswordToggle
            required
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id="rememberMe"
                checked={rememberMe}
                onCheckedChange={setRememberMe}
              />
              <label
                htmlFor="rememberMe"
                className="text-sm text-gray-700 arabic-text cursor-pointer"
              >
                تذكرني
              </label>
            </div>

            <Link
              href="/auth/forgot-password"
              className="text-sm text-[#064635] hover:text-[#053d2f] arabic-text transition-colors"
            >
              نسيت كلمة المرور؟
            </Link>
          </div>

          <AuthButton
            type="submit"
            loading={isSubmitting}
            loadingText="جاري تسجيل الدخول..."
          >
            تسجيل الدخول
          </AuthButton>

          <div className="text-center">
            <p className="text-gray-600 arabic-text">
              ليس لديك حساب؟{' '}
              <Link
                href="/auth/register"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                إنشاء حساب جديد
              </Link>
            </p>
          </div>
        </form>
      </AuthLayout>
    </GuestRoute>
  )
}
