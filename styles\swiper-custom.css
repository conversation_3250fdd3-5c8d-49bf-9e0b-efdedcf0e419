/* Custom Swiper Styles for Arabic RTL Design */

/* Pagination Bullets */
.swiper-pagination {
  position: relative !important;
  margin-top: 2rem !important;
}

.swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  margin: 0 6px !important;
  background: #d1d5db !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
}

.swiper-pagination-bullet-active {
  background: #064635 !important;
  width: 32px !important;
  border-radius: 6px !important;
  transform: scale(1.1) !important;
}

/* RTL Direction Support */
.swiper[dir="rtl"] .swiper-pagination-bullet {
  margin: 0 6px !important;
}

/* Navigation Buttons (if needed) */
.swiper-button-next,
.swiper-button-prev {
  color: #064635 !important;
  background: white !important;
  width: 44px !important;
  height: 44px !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: #064635 !important;
  color: white !important;
  transform: scale(1.1) !important;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  font-size: 16px !important;
  font-weight: bold !important;
}

/* RTL Navigation */
.swiper[dir="rtl"] .swiper-button-next {
  left: 10px !important;
  right: auto !important;
}

.swiper[dir="rtl"] .swiper-button-prev {
  right: 10px !important;
  left: auto !important;
}

/* Slide Transitions */
.swiper-slide {
  transition: transform 0.3s ease !important;
}

.swiper-slide:hover {
  transform: translateY(-4px) !important;
}

/* Loading State */
.swiper-wrapper {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
    margin: 0 4px !important;
  }
  
  .swiper-pagination-bullet-active {
    width: 24px !important;
  }
  
  .swiper-button-next,
  .swiper-button-prev {
    width: 36px !important;
    height: 36px !important;
  }
  
  .swiper-button-next::after,
  .swiper-button-prev::after {
    font-size: 14px !important;
  }
}

/* Arabic Font Support */
.swiper-slide .arabic-text,
.swiper-slide .arabic-heading {
  font-family: 'Vazirmatn', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Enhanced Hover Effects */
.swiper-slide .group:hover {
  transform: translateY(-8px) scale(1.02) !important;
}

/* Smooth Scrolling */
.swiper {
  overflow: visible !important;
}

.swiper-wrapper {
  padding: 0 !important;
}
