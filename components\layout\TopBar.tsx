"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { X, Globe } from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  getCurrentAnnouncement, 
  shouldShowTopBar, 
  dismissTopBar,
  LANGUAGE_CONFIG 
} from '@/lib/site-config'
import type { SiteAnnouncement } from '@/lib/site-config'

interface TopBarProps {
  onDismiss?: () => void
  className?: string
}

export default function TopBar({ onDismiss, className = '' }: TopBarProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [announcement, setAnnouncement] = useState<SiteAnnouncement | null>(null)
  const [currentDate, setCurrentDate] = useState<string>('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Mark as client-side to prevent hydration mismatch
    setIsClient(true)

    // Check if topbar should be shown (client-side only)
    const shouldShow = shouldShowTopBar()
    const currentAnnouncement = getCurrentAnnouncement()

    setIsVisible(shouldShow && !!currentAnnouncement)
    setAnnouncement(currentAnnouncement)

    // Set current date in Arabic (client-side only)
    const today = new Date()
    const arabicDate = format(today, 'EEEE d MMMM yyyy', { locale: ar })
    setCurrentDate(arabicDate)
  }, [])

  const handleDismiss = () => {
    dismissTopBar()
    setIsVisible(false)
    onDismiss?.()
  }

  // Don't render anything on server-side to prevent hydration mismatch
  if (!isClient) {
    return null
  }

  if (!isVisible || !announcement) {
    return null
  }

  const renderPromoContent = () => (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center space-x-3 rtl:space-x-reverse">
        {announcement.emoji && (
          <span className="text-lg">{announcement.emoji}</span>
        )}
        <span className="text-sm font-medium arabic-text text-saudi-700">
          {announcement.message}
        </span>
        {announcement.link && announcement.linkText && (
          <Link
            href={announcement.link}
            className="text-sm font-medium text-saudi-primary hover:text-saudi-secondary transition-colors duration-200 arabic-text underline"
          >
            {announcement.linkText}
          </Link>
        )}
      </div>
      
      <button
        onClick={handleDismiss}
        className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-saudi-200 transition-colors duration-200 group"
        aria-label="إغلاق الإشعار"
      >
        <X className="w-4 h-4 text-saudi-600 group-hover:text-saudi-800" />
      </button>
    </div>
  )

  const renderInfoContent = () => (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center space-x-4 rtl:space-x-reverse">
        <span className="text-sm text-gray-600 arabic-text">
          {currentDate}
        </span>
        <div className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
          <Globe className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600">
            {LANGUAGE_CONFIG.labels.ar} | {LANGUAGE_CONFIG.labels.en}
          </span>
        </div>
      </div>
      
      <button
        onClick={handleDismiss}
        className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-gray-200 transition-colors duration-200 group"
        aria-label="إغلاق الشريط العلوي"
      >
        <X className="w-4 h-4 text-gray-500 group-hover:text-gray-700" />
      </button>
    </div>
  )

  const getBackgroundClasses = () => {
    switch (announcement.type) {
      case 'promo':
        return 'bg-gradient-to-r from-saudi-100 to-saudi-50 border-b border-saudi-200'
      case 'info':
        return 'bg-gray-50 border-b border-gray-200'
      default:
        return 'bg-saudi-100 border-b border-saudi-200'
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`w-full overflow-hidden ${className}`}
      >
        <div className={`h-10 flex items-center px-4 ${getBackgroundClasses()}`}>
          <div className="container mx-auto">
            {announcement.type === 'promo' ? renderPromoContent() : renderInfoContent()}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

// Custom hook for TopBar state management
export const useTopBarState = () => {
  const [isTopBarVisible, setIsTopBarVisible] = useState(false)

  useEffect(() => {
    // Only run on client-side to prevent hydration mismatch
    const shouldShow = shouldShowTopBar()
    const announcement = getCurrentAnnouncement()
    setIsTopBarVisible(shouldShow && !!announcement)
  }, [])

  const handleTopBarDismiss = () => {
    setIsTopBarVisible(false)
  }

  return {
    isTopBarVisible,
    handleTopBarDismiss
  }
}

// Mobile-optimized TopBar for smaller screens
export const MobileTopBar = ({ onDismiss, className = '' }: TopBarProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const [announcement, setAnnouncement] = useState<SiteAnnouncement | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Mark as client-side to prevent hydration mismatch
    setIsClient(true)

    const shouldShow = shouldShowTopBar()
    const currentAnnouncement = getCurrentAnnouncement()

    setIsVisible(shouldShow && !!currentAnnouncement)
    setAnnouncement(currentAnnouncement)
  }, [])

  const handleDismiss = () => {
    dismissTopBar()
    setIsVisible(false)
    onDismiss?.()
  }

  // Don't render anything on server-side to prevent hydration mismatch
  if (!isClient) {
    return null
  }

  if (!isVisible || !announcement) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: 'auto', opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`w-full overflow-hidden lg:hidden ${className}`}
      >
        <div className={`h-12 flex items-center px-3 ${
          announcement.type === 'promo' 
            ? 'bg-gradient-to-r from-saudi-100 to-saudi-50 border-b border-saudi-200'
            : 'bg-gray-50 border-b border-gray-200'
        }`}>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2 rtl:space-x-reverse flex-1 min-w-0">
              {announcement.emoji && (
                <span className="text-base flex-shrink-0">{announcement.emoji}</span>
              )}
              <span className="text-xs font-medium arabic-text text-saudi-700 truncate">
                {announcement.type === 'promo' 
                  ? 'خصم 30٪ على قوالب React!' 
                  : announcement.message
                }
              </span>
            </div>
            
            <button
              onClick={handleDismiss}
              className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-saudi-200 transition-colors duration-200 group flex-shrink-0 mr-2 rtl:mr-0 rtl:ml-2"
              aria-label="إغلاق"
            >
              <X className="w-4 h-4 text-saudi-600 group-hover:text-saudi-800" />
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
