"use client"

import React from 'react'
import { useRequireAuth } from '@/lib/auth/hooks'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export default function ProtectedRoute({ 
  children, 
  fallback,
  redirectTo = '/auth/login' 
}: ProtectedRouteProps) {
  const { loading, isAuthenticated } = useRequireAuth(redirectTo)

  if (loading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#064635]" />
          <p className="text-gray-600 arabic-text">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Redirect is handled by useRequireAuth hook
  }

  return <>{children}</>
}
