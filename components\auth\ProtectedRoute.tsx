"use client"

import React from 'react'
import { useRequireAuth } from '@/lib/auth/hooks'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export default function ProtectedRoute({
  children,
  fallback,
  redirectTo = '/auth/login'
}: ProtectedRouteProps) {
  const { loading, isAuthenticated } = useRequireAuth(redirectTo)

  // Skip loading state for instant navigation
  if (!loading && !isAuthenticated) {
    return null // Redirect is handled by useRequireAuth hook
  }

  return <>{children}</>
}
