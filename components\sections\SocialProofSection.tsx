"use client"

import React, { useRef } from 'react'
import Image from 'next/image'
import { <PERSON>, Quote, Users, Award, TrendingUp, ChevronLeft, ChevronRight } from 'lucide-react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'
import { TESTIMONIALS } from '@/lib/constants'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

export default function SocialProofSection() {
  const swiperRef = useRef<SwiperType>()

  const handlePrevSlide = () => {
    swiperRef.current?.slidePrev()
  }

  const handleNextSlide = () => {
    swiperRef.current?.slideNext()
  }

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#064635] rounded-full blur-3xl transform translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-saudi-primary/10 text-saudi-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Quote className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
            <span className="arabic-text">شهادات العملاء</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
            آراء العملاء
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic-text leading-relaxed mb-12">
            اكتشف ما يقوله عملاؤنا الراضون عن تجربتهم مع قوالبنا المتميزة
          </p>

          {/* Enhanced Overall Rating */}
          <div className="inline-flex items-center bg-white border-2 border-saudi-primary/10 rounded-3xl px-8 py-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="flex items-center ml-8 rtl:ml-0 rtl:mr-8">
              <div className="text-5xl font-bold text-saudi-primary ml-3 rtl:ml-0 rtl:mr-3">4.9</div>
              <div className="flex flex-col">
                <div className="flex items-center mb-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-6 h-6 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-600 arabic-text font-medium">من 5 نجوم</div>
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">2,500+</div>
              <div className="text-sm text-gray-600 arabic-text font-medium">مستخدم راضي</div>
            </div>
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center mt-8 space-x-4 rtl:space-x-reverse">
            <button
              onClick={handlePrevSlide}
              className="group flex items-center justify-center w-12 h-12 bg-white border-2 border-gray-200 rounded-full hover:border-saudi-primary hover:bg-saudi-primary transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ChevronRight className="w-5 h-5 text-gray-600 group-hover:text-white rtl:rotate-180 transition-colors duration-300" />
            </button>

            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-saudi-primary rounded-full"></div>
              <div className="w-8 h-2 bg-saudi-primary/20 rounded-full"></div>
              <div className="w-2 h-2 bg-saudi-primary/40 rounded-full"></div>
            </div>

            <button
              onClick={handleNextSlide}
              className="group flex items-center justify-center w-12 h-12 bg-white border-2 border-gray-200 rounded-full hover:border-saudi-primary hover:bg-saudi-primary transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ChevronLeft className="w-5 h-5 text-gray-600 group-hover:text-white rtl:rotate-180 transition-colors duration-300" />
            </button>
          </div>
        </div>

        {/* Enhanced Testimonials Carousel */}
        <div className="relative mb-20">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={24}
            slidesPerView={1}
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              bulletClass: 'swiper-pagination-bullet !bg-gray-300 !w-3 !h-3 !mx-1',
              bulletActiveClass: 'swiper-pagination-bullet-active !bg-saudi-primary !w-8 !rounded-full',
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 24,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 28,
              },
              1280: {
                slidesPerView: 3,
                spaceBetween: 32,
              },
            }}
            onSwiper={(swiper) => {
              swiperRef.current = swiper
            }}
            dir="rtl"
            className="!pb-16"
            loop={true}
            centeredSlides={false}
            watchSlidesProgress={true}
          >
            {TESTIMONIALS.map((testimonial) => (
              <SwiperSlide key={testimonial.id}>
                <div className="group bg-white rounded-3xl border border-gray-100 p-8 relative hover:shadow-2xl hover:shadow-saudi-primary/10 hover:border-saudi-primary/20 transition-all duration-500 transform hover:-translate-y-2 hover:scale-[1.02]">
                  {/* Enhanced Quote Icon */}
                  <div className="absolute -top-4 right-6 rtl:right-auto rtl:left-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-saudi-primary to-saudi-secondary rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <Quote className="w-5 h-5 text-white" />
                    </div>
                  </div>

                  {/* Enhanced Rating */}
                  <div className="flex items-center mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-5 h-5 ${
                          i < testimonial.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="ml-2 rtl:ml-0 rtl:mr-2 text-sm font-semibold text-gray-700">
                      {testimonial.rating}.0
                    </span>
                  </div>

                  {/* Enhanced Content */}
                  <p className="text-gray-700 arabic-text leading-relaxed mb-8 text-lg">
                    "{testimonial.content}"
                  </p>

                  {/* Enhanced Author */}
                  <div className="flex items-center">
                    <div className="w-14 h-14 rounded-full overflow-hidden ml-4 rtl:ml-0 rtl:mr-4 ring-2 ring-saudi-primary/10 group-hover:ring-saudi-primary/30 transition-all duration-300">
                      <Image
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        width={56}
                        height={56}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="font-bold text-gray-900 arabic-text text-lg">{testimonial.name}</div>
                      <div className="text-sm text-saudi-primary arabic-text font-medium">{testimonial.role}</div>
                      <div className="text-xs text-gray-500 arabic-text">{testimonial.company}</div>
                    </div>
                  </div>

                  {/* Gradient Border Effect */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-saudi-primary/5 via-transparent to-saudi-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Enhanced Trust Indicators */}
        <div className="bg-gradient-to-br from-saudi-primary/5 via-white to-saudi-primary/5 rounded-3xl border border-saudi-primary/10 p-12 mb-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-saudi-primary to-saudi-secondary rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                <Users className="w-10 h-10 text-white" />
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-3">15,000+</div>
              <div className="text-gray-600 arabic-text font-medium">مطور سعيد</div>
            </div>

            <div className="group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                <Award className="w-10 h-10 text-white" />
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-3">800+</div>
              <div className="text-gray-600 arabic-text font-medium">قالب عالي الجودة</div>
            </div>

            <div className="group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-3">99%</div>
              <div className="text-gray-600 arabic-text font-medium">معدل الرضا</div>
            </div>

            <div className="group">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-3">24/7</div>
              <div className="text-gray-600 arabic-text font-medium">دعم فني</div>
            </div>
          </div>
        </div>

        {/* Enhanced Featured Companies */}
        <div className="text-center mb-16">
          <p className="text-gray-600 arabic-text mb-8 text-lg">يثق بنا المطورون في أفضل الشركات</p>
          <div className="flex items-center justify-center space-x-12 rtl:space-x-reverse opacity-70 hover:opacity-100 transition-opacity duration-300">
            {/* Enhanced Company Logos */}
            <div className="w-32 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl flex items-center justify-center hover:shadow-lg transition-shadow duration-300">
              <span className="text-gray-600 text-sm font-medium arabic-text">تقنية المستقبل</span>
            </div>
            <div className="w-32 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl flex items-center justify-center hover:shadow-lg transition-shadow duration-300">
              <span className="text-gray-600 text-sm font-medium arabic-text">الابتكار الرقمي</span>
            </div>
            <div className="w-32 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl flex items-center justify-center hover:shadow-lg transition-shadow duration-300">
              <span className="text-gray-600 text-sm font-medium arabic-text">سمارت تك</span>
            </div>
            <div className="w-32 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl flex items-center justify-center hover:shadow-lg transition-shadow duration-300">
              <span className="text-gray-600 text-sm font-medium arabic-text">كود كريتف</span>
            </div>
          </div>
        </div>

        {/* Enhanced CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-saudi-primary to-saudi-secondary rounded-3xl p-8 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
            </div>

            <div className="relative z-10">
              <h3 className="text-2xl font-bold text-white mb-4 arabic-heading">
                انضم إلى آلاف المطورين الراضين
              </h3>
              <p className="text-white/90 mb-6 arabic-text">
                ابدأ رحلتك مع أفضل القوالب البرمجية
              </p>
              <a
                href="/templates"
                className="inline-flex items-center bg-white text-saudi-primary hover:bg-gray-50 px-8 py-3 rounded-xl font-bold arabic-text transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              >
                ابدأ الآن
                <svg className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
