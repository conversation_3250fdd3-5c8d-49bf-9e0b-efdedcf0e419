"use client"

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { User, Mail, Phone, Lock, Save, Camera } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthForm } from '@/lib/auth/hooks'
import { updateProfileSchema, changePasswordSchema, type UpdateProfileFormData, type ChangePasswordFormData } from '@/lib/auth/types'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import AuthSuccess from '@/components/auth/AuthSuccess'
import Header from '@/components/layout/Header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

export default function ProfilePage() {
  const { state, updateProfile, updatePassword } = useAuth()
  const { user, profile } = state
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'profile' | 'password'>('profile')

  // Profile form
  const profileForm = useForm<UpdateProfileFormData>({
    resolver: zodResolver(updateProfileSchema),
    defaultValues: {
      fullName: profile?.full_name || '',
      phone: profile?.phone || '',
    },
  })

  // Password form
  const passwordForm = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  })

  const onUpdateProfile = async (data: UpdateProfileFormData) => {
    clearErrors()
    setSuccessMessage(null)
    
    await handleSubmit(
      async () => {
        const result = await updateProfile(data)
        return result
      },
      () => {
        setSuccessMessage('تم تحديث الملف الشخصي بنجاح')
        toast.success('تم تحديث الملف الشخصي بنجاح')
      }
    )
  }

  const onChangePassword = async (data: ChangePasswordFormData) => {
    clearErrors()
    setSuccessMessage(null)
    
    await handleSubmit(
      async () => {
        const result = await updatePassword(data.newPassword)
        return result
      },
      () => {
        setSuccessMessage('تم تحديث كلمة المرور بنجاح')
        toast.success('تم تحديث كلمة المرور بنجاح')
        passwordForm.reset()
      }
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 arabic-heading mb-2">
              الملف الشخصي
            </h1>
            <p className="text-gray-600 arabic-text">
              إدارة معلوماتك الشخصية وإعدادات الحساب
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="md:col-span-1">
              <Card>
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <div className="w-20 h-20 bg-[#064635] rounded-full flex items-center justify-center mx-auto mb-4">
                      <User className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 arabic-text">
                      {profile?.full_name || user?.email}
                    </h3>
                    <p className="text-sm text-gray-500 arabic-text">
                      {user?.email}
                    </p>
                  </div>

                  <nav className="space-y-2">
                    <button
                      onClick={() => setActiveTab('profile')}
                      className={`w-full text-right px-3 py-2 rounded-lg text-sm font-medium transition-colors arabic-text ${
                        activeTab === 'profile'
                          ? 'bg-[#064635] text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      المعلومات الشخصية
                    </button>
                    <button
                      onClick={() => setActiveTab('password')}
                      className={`w-full text-right px-3 py-2 rounded-lg text-sm font-medium transition-colors arabic-text ${
                        activeTab === 'password'
                          ? 'bg-[#064635] text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      تغيير كلمة المرور
                    </button>
                  </nav>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="md:col-span-3">
              {activeTab === 'profile' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="arabic-heading">المعلومات الشخصية</CardTitle>
                    <CardDescription className="arabic-text">
                      قم بتحديث معلوماتك الشخصية هنا
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={profileForm.handleSubmit(onUpdateProfile)} className="space-y-6">
                      <AuthError error={errors.general} onDismiss={clearErrors} />
                      <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

                      <AuthInput
                        {...profileForm.register('fullName')}
                        label="الاسم الكامل"
                        type="text"
                        icon={<User className="w-5 h-5" />}
                        error={profileForm.formState.errors.fullName?.message}
                        placeholder="أدخل اسمك الكامل"
                        required
                      />

                      <AuthInput
                        label="البريد الإلكتروني"
                        type="email"
                        icon={<Mail className="w-5 h-5" />}
                        value={user?.email || ''}
                        disabled
                        description="لا يمكن تغيير البريد الإلكتروني"
                      />

                      <AuthInput
                        {...profileForm.register('phone')}
                        label="رقم الهاتف"
                        type="tel"
                        icon={<Phone className="w-5 h-5" />}
                        error={profileForm.formState.errors.phone?.message}
                        placeholder="أدخل رقم هاتفك"
                      />

                      <AuthButton
                        type="submit"
                        loading={isSubmitting}
                        loadingText="جاري التحديث..."
                        className="w-auto"
                      >
                        <Save className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                        حفظ التغييرات
                      </AuthButton>
                    </form>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'password' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="arabic-heading">تغيير كلمة المرور</CardTitle>
                    <CardDescription className="arabic-text">
                      قم بتحديث كلمة المرور لحماية حسابك
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={passwordForm.handleSubmit(onChangePassword)} className="space-y-6">
                      <AuthError error={errors.general} onDismiss={clearErrors} />
                      <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

                      <AuthInput
                        {...passwordForm.register('currentPassword')}
                        label="كلمة المرور الحالية"
                        type="password"
                        icon={<Lock className="w-5 h-5" />}
                        error={passwordForm.formState.errors.currentPassword?.message}
                        placeholder="أدخل كلمة المرور الحالية"
                        showPasswordToggle
                        required
                      />

                      <AuthInput
                        {...passwordForm.register('newPassword')}
                        label="كلمة المرور الجديدة"
                        type="password"
                        icon={<Lock className="w-5 h-5" />}
                        error={passwordForm.formState.errors.newPassword?.message}
                        placeholder="أدخل كلمة مرور جديدة"
                        showPasswordToggle
                        required
                      />

                      <AuthInput
                        {...passwordForm.register('confirmPassword')}
                        label="تأكيد كلمة المرور الجديدة"
                        type="password"
                        icon={<Lock className="w-5 h-5" />}
                        error={passwordForm.formState.errors.confirmPassword?.message}
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                        showPasswordToggle
                        required
                      />

                      <AuthButton
                        type="submit"
                        loading={isSubmitting}
                        loadingText="جاري التحديث..."
                        className="w-auto"
                      >
                        <Lock className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                        تحديث كلمة المرور
                      </AuthButton>
                    </form>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
