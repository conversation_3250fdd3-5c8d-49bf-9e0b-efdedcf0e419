import React from 'react'
import Link from 'next/link'
import { ArrowLeft, Star, Users, Download } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ROUTES } from '@/lib/constants'
import RotatingText from '@/components/ui/RotatingText'

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-saudi-50 to-white py-20 lg:py-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231B7B3A' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right rtl:lg:text-right">
            {/* Badge */}
            <div className="inline-flex items-center bg-saudi-100 text-saudi-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Star className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
              <span className="arabic-text">الأكثر ثقة في المنطقة</span>
            </div>

            {/* Main Headline with Rotating Text */}
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
              ابدأ رحلتك مع{' '}
              <span className="text-saudi-primary">أقوى قوالب</span>{' '}
              <RotatingText
                words={['التطبيقات', 'التصاميم', 'مواقع الويب', 'لوحات التحكم', 'المتاجر الإلكترونية']}
                className="text-saudi-primary font-bold"
                interval={2500}
              />{' '}
              الجاهزة
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed max-w-2xl mx-auto lg:mx-0">
              اكتشف مجموعة متنوعة من القوالب عالية الجودة لتطوير مشاريعك بسرعة واحترافية. 
              قوالب تطبيقات، واجهات مستخدم، ومواقع ويب جاهزة للاستخدام.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start rtl:lg:justify-start mb-12">
              <Button asChild size="lg" className="btn-saudi-primary text-lg px-8 py-4">
                <Link href={ROUTES.TEMPLATES} className="flex items-center">
                  <span className="arabic-text">تصفح القوالب الآن</span>
                  <ArrowLeft className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2 rtl:rotate-180" />
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg" className="text-lg px-8 py-4 border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white">
                <Link href="#categories" className="arabic-text">
                  استكشف الفئات
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-md mx-auto lg:mx-0">
              <div className="text-center lg:text-right rtl:lg:text-right">
                <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1">500+</div>
                <div className="text-sm text-gray-600 arabic-text">قالب متاح</div>
              </div>
              <div className="text-center lg:text-right rtl:lg:text-right">
                <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1">10K+</div>
                <div className="text-sm text-gray-600 arabic-text">مطور راضي</div>
              </div>
              <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1 text-center lg:text-right rtl:lg:text-right">
                <div className="flex items-center justify-center lg:justify-start rtl:lg:justify-start">
                  <span>4.9</span>
                  <Star className="w-5 h-5 text-yellow-400 fill-current mr-1 rtl:mr-0 rtl:ml-1" />
                </div>
                <div className="text-sm text-gray-600 arabic-text">تقييم العملاء</div>
              </div>
            </div>
          </div>

          {/* Illustration/Image */}
          <div className="relative">
            {/* Main Illustration Container */}
            <div className="relative bg-gradient-to-br from-saudi-100 to-saudi-50 rounded-3xl p-8 lg:p-12">
              {/* Floating Cards */}
              <div className="relative">
                {/* Template Card 1 */}
                <div className="bg-white rounded-xl shadow-lg p-6 mb-6 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-saudi-gradient rounded-lg flex items-center justify-center">
                      <Download className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right rtl:text-right">
                      <div className="text-sm font-medium text-gray-900 arabic-text">تطبيق التجارة الإلكترونية</div>
                      <div className="text-xs text-gray-500">React Native</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 mr-1 rtl:mr-0 rtl:ml-1">4.8</span>
                    </div>
                    <div className="text-saudi-primary font-bold">299 ر.س</div>
                  </div>
                </div>

                {/* Template Card 2 */}
                <div className="bg-white rounded-xl shadow-lg p-6 transform -rotate-2 hover:rotate-0 transition-transform duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right rtl:text-right">
                      <div className="text-sm font-medium text-gray-900 arabic-text">لوحة تحكم إدارية</div>
                      <div className="text-xs text-gray-500">React + TypeScript</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 mr-1 rtl:mr-0 rtl:ml-1">4.9</span>
                    </div>
                    <div className="text-saudi-primary font-bold">199 ر.س</div>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-saudi-200 rounded-full opacity-50"></div>
              <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-saudi-300 rounded-full opacity-30"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
