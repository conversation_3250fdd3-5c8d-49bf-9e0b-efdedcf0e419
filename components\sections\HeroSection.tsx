"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Star, Users, Download, Code, Smartphone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ROUTES, FEATURED_TEMPLATES } from '@/lib/constants'
import RotatingText from '@/components/animations/TextAnimations/RotatingText/RotatingText'

export default function HeroSection() {
  const [currentTemplateIndex, setCurrentTemplateIndex] = useState(0)
  const [isClient, setIsClient] = useState(false)

  // Get first 4 templates for animation
  const animatedTemplates = FEATURED_TEMPLATES.slice(0, 4)

  // Client-side only animation to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    const interval = setInterval(() => {
      setCurrentTemplateIndex((prev) => (prev + 1) % animatedTemplates.length)
    }, 2500) // 2.5 second intervals

    return () => clearInterval(interval)
  }, [isClient, animatedTemplates.length])

  // Icon mapping for template categories
  const getTemplateIcon = (category: string) => {
    switch (category) {
      case 'apps':
        return Smartphone
      case 'ui-ux':
        return Code
      case 'web':
        return Download
      default:
        return Users
    }
  }

  return (
    <section className="relative bg-gradient-to-br from-saudi-50 to-white py-20 lg:py-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231B7B3A' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right rtl:lg:text-right">
            {/* Badge */}
            <div className="inline-flex items-center bg-saudi-100 text-saudi-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Star className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
              <span className="arabic-text">الأكثر ثقة في المنطقة</span>
            </div>

            {/* Main Headline with Rotating Text */}
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
              ابدأ رحلتك مع{' '}
              <span className="text-saudi-primary">أقوى قوالب</span>{' '}
              <RotatingText
                texts={['التطبيقات', 'التصاميم', 'مواقع الويب', 'لوحات التحكم', 'المتاجر الإلكترونية']}
                rotationInterval={2500}
                auto={true}
                loop={true}
                splitBy="words"
                animatePresenceMode="wait"
                transition={{
                  type: "spring",
                  damping: 25,
                  stiffness: 300,
                  duration: 0.3
                }}
                initial={{ y: "100%", opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: "-100%", opacity: 0 }}
                mainClassName="inline-flex items-center justify-center bg-saudi-primary text-white px-4 py-2 rounded-lg shadow-lg font-bold text-4xl lg:text-6xl"
                elementLevelClassName="inline-block"
                splitLevelClassName="inline-flex"
              />{' '}
              الجاهزة
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-600 mb-8 arabic-text leading-relaxed max-w-2xl mx-auto lg:mx-0">
              اكتشف مجموعة متنوعة من القوالب عالية الجودة لتطوير مشاريعك بسرعة واحترافية. 
              قوالب تطبيقات، واجهات مستخدم، ومواقع ويب جاهزة للاستخدام.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start rtl:lg:justify-start mb-12">
              <Button asChild size="lg" className="btn-saudi-primary text-lg px-8 py-4">
                <Link href={ROUTES.TEMPLATES} className="flex items-center">
                  <span className="arabic-text">تصفح القوالب الآن</span>
                  <ArrowLeft className="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2 rtl:rotate-180" />
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg" className="text-lg px-8 py-4 border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white">
                <Link href="#categories" className="arabic-text">
                  استكشف الفئات
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-md mx-auto lg:mx-0">
              <div className="text-center lg:text-right rtl:lg:text-right">
                <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1">500+</div>
                <div className="text-sm text-gray-600 arabic-text">قالب متاح</div>
              </div>
              <div className="text-center lg:text-right rtl:lg:text-right">
                <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1">10K+</div>
                <div className="text-sm text-gray-600 arabic-text">مطور راضي</div>
              </div>
              <div className="text-2xl lg:text-3xl font-bold text-saudi-primary mb-1 text-center lg:text-right rtl:lg:text-right">
                <div className="flex items-center justify-center lg:justify-start rtl:lg:justify-start">
                  <span>4.9</span>
                  <Star className="w-5 h-5 text-yellow-400 fill-current mr-1 rtl:mr-0 rtl:ml-1" />
                </div>
                <div className="text-sm text-gray-600 arabic-text">تقييم العملاء</div>
              </div>
            </div>
          </div>

          {/* Illustration/Image */}
          <div className="relative">
            {/* Main Illustration Container */}
            <div className="relative bg-gradient-to-br from-saudi-100 to-saudi-50 rounded-3xl p-8 lg:p-12">
              {/* Animated Floating Template Cards */}
              <div className="relative min-h-[300px]">
                {isClient && (
                  <AnimatePresence mode="wait">
                    {animatedTemplates.map((template, index) => {
                      if (index !== currentTemplateIndex) return null

                      const IconComponent = getTemplateIcon(template.category)

                      return (
                        <motion.div
                          key={`${template.id}-${index}`}
                          initial={{
                            opacity: 0,
                            y: 50,
                            scale: 0.8,
                            rotate: index % 2 === 0 ? 5 : -5
                          }}
                          animate={{
                            opacity: 1,
                            y: 0,
                            scale: 1,
                            rotate: index % 2 === 0 ? 3 : -2
                          }}
                          exit={{
                            opacity: 0,
                            y: -50,
                            scale: 0.8,
                            rotate: index % 2 === 0 ? -5 : 5
                          }}
                          transition={{
                            duration: 0.6,
                            ease: "easeInOut",
                            type: "spring",
                            damping: 20,
                            stiffness: 300
                          }}
                          whileHover={{
                            rotate: 0,
                            scale: 1.05,
                            y: -10,
                            transition: { duration: 0.3 }
                          }}
                          className="absolute inset-0"
                        >
                          {/* Primary Card */}
                          <div className="bg-white rounded-xl shadow-xl p-6 mb-6 border border-gray-100 hover:shadow-2xl transition-shadow duration-300">
                            <div className="flex items-center justify-between mb-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-saudi-primary to-saudi-secondary rounded-lg flex items-center justify-center shadow-lg">
                                <IconComponent className="w-6 h-6 text-white" />
                              </div>
                              <div className="text-right rtl:text-right flex-1 mr-4 rtl:mr-0 rtl:ml-4">
                                <div className="text-sm font-bold text-gray-900 arabic-text line-clamp-1">
                                  {template.title}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {template.tags[0]} • {template.tags[1]}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="text-sm text-gray-600 mr-1 rtl:mr-0 rtl:ml-1 font-medium">
                                  {template.rating}
                                </span>
                                <span className="text-xs text-gray-400 mr-1 rtl:mr-0 rtl:ml-1">
                                  ({template.reviewCount})
                                </span>
                              </div>
                              <div className="text-saudi-primary font-bold text-lg">
                                {template.price} ر.س
                              </div>
                            </div>
                          </div>

                          {/* Secondary Card (Staggered) */}
                          <motion.div
                            initial={{ opacity: 0, x: 30, rotate: -3 }}
                            animate={{ opacity: 0.8, x: 20, rotate: -2 }}
                            exit={{ opacity: 0, x: 50, rotate: -5 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            className="bg-white rounded-xl shadow-lg p-4 transform translate-x-4 -translate-y-4 border border-gray-100"
                          >
                            <div className="flex items-center justify-between">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <Code className="w-4 h-4 text-white" />
                              </div>
                              <div className="text-right rtl:text-right flex-1 mr-3 rtl:mr-0 rtl:ml-3">
                                <div className="text-xs font-medium text-gray-700 arabic-text">
                                  {animatedTemplates[(currentTemplateIndex + 1) % animatedTemplates.length]?.title.slice(0, 20)}...
                                </div>
                                <div className="text-xs text-gray-500">
                                  {animatedTemplates[(currentTemplateIndex + 1) % animatedTemplates.length]?.tags[0]}
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        </motion.div>
                      )
                    })}
                  </AnimatePresence>
                )}

                {/* Static fallback for SSR */}
                {!isClient && (
                  <div className="bg-white rounded-xl shadow-lg p-6 mb-6 transform rotate-3">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-saudi-primary to-saudi-secondary rounded-lg flex items-center justify-center">
                        <Download className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-right rtl:text-right">
                        <div className="text-sm font-medium text-gray-900 arabic-text">تطبيق التجارة الإلكترونية</div>
                        <div className="text-xs text-gray-500">React Native</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 mr-1 rtl:mr-0 rtl:ml-1">4.8</span>
                      </div>
                      <div className="text-saudi-primary font-bold">299 ر.س</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-saudi-200 rounded-full opacity-50"></div>
              <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-saudi-300 rounded-full opacity-30"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
