# Internationalization (i18n) Usage Guide

## Overview

The i18n system has been restructured to use modular translation files for better organization and maintainability. Translation files are now organized by component/section rather than having everything in a single file.

## File Structure

```
locales/
├── ar/
│   ├── common.json     # Shared translations used across multiple components
│   ├── hero.json       # Hero section translations
│   ├── navbar.json     # Navigation/header translations
│   └── footer.json     # Footer translations
└── en/
    ├── common.json     # Shared translations used across multiple components
    ├── hero.json       # Hero section translations
    ├── navbar.json     # Navigation/header translations
    └── footer.json     # Footer translations
```

## Translation Structure

The translations are now organized into the following sections:

- `t.common` - Common translations used across multiple components
- `t.hero` - Hero section specific translations
- `t.navbar` - Navigation/header specific translations
- `t.footer` - Footer specific translations

## Usage Examples

### Using Common Translations

```tsx
import { useI18n } from '@/contexts/I18nContext';

function MyComponent() {
  const { t, locale } = useI18n();
  
  return (
    <div>
      <h1>{t.common.welcome}</h1>
      <p>{t.common.language}: {locale}</p>
      <button>{t.common.save}</button>
    </div>
  );
}
```

### Using Hero Section Translations

```tsx
import { useI18n } from '@/contexts/I18nContext';

function HeroComponent() {
  const { t } = useI18n();
  
  return (
    <section>
      <h1>{t.hero.title}</h1>
      <p>{t.hero.subtitle}</p>
      <p>{t.hero.description}</p>
      <button>{t.hero.cta.primary}</button>
      <button>{t.hero.cta.secondary}</button>
    </section>
  );
}
```

### Using Navbar Translations

```tsx
import { useI18n } from '@/contexts/I18nContext';

function NavbarComponent() {
  const { t } = useI18n();
  
  return (
    <nav>
      <div>{t.navbar.brand.name}</div>
      <ul>
        <li>{t.navbar.navigation.home}</li>
        <li>{t.navbar.navigation.templates}</li>
        <li>{t.navbar.navigation.about}</li>
      </ul>
      <button>{t.navbar.actions.login}</button>
    </nav>
  );
}
```

### Using Footer Translations

```tsx
import { useI18n } from '@/contexts/I18nContext';

function FooterComponent() {
  const { t } = useI18n();
  
  return (
    <footer>
      <h3>{t.footer.company.name}</h3>
      <p>{t.footer.company.description}</p>
      
      <div>
        <h4>{t.footer.links.product.title}</h4>
        <ul>
          <li>{t.footer.links.product.templates}</li>
          <li>{t.footer.links.product.pricing}</li>
        </ul>
      </div>
      
      <div>
        <h4>{t.footer.newsletter.title}</h4>
        <p>{t.footer.newsletter.description}</p>
        <input placeholder={t.footer.newsletter.placeholder} />
        <button>{t.footer.newsletter.subscribe}</button>
      </div>
    </footer>
  );
}
```

## Adding New Translations

### 1. Add to Translation Files

Add your new translations to the appropriate JSON files:

**locales/ar/common.json:**
```json
{
  "newFeature": {
    "title": "ميزة جديدة",
    "description": "وصف الميزة الجديدة"
  }
}
```

**locales/en/common.json:**
```json
{
  "newFeature": {
    "title": "New Feature",
    "description": "Description of the new feature"
  }
}
```

### 2. Update TypeScript Types

Update the corresponding type in `types/Translations.d.ts`:

```typescript
export type CommonTranslations = {
  // ... existing properties
  newFeature: {
    title: string;
    description: string;
  };
};
```

### 3. Use in Components

```tsx
function NewFeatureComponent() {
  const { t } = useI18n();
  
  return (
    <div>
      <h2>{t.common.newFeature.title}</h2>
      <p>{t.common.newFeature.description}</p>
    </div>
  );
}
```

## Benefits of This Structure

1. **Better Organization**: Translations are grouped by component/section
2. **Team Collaboration**: Different team members can work on translations for different sections without conflicts
3. **Maintainability**: Easier to find and update specific translations
4. **Scalability**: Easy to add new sections as the application grows
5. **Type Safety**: Full TypeScript support with proper type checking

## Migration from Old Structure

If you have existing components using the old flat structure, update them as follows:

**Old:**
```tsx
{t.language}        // ❌ Old way
{t.hero.title}      // ❌ Old way
{t.test.title}      // ❌ Old way
```

**New:**
```tsx
{t.common.language}      // ✅ New way
{t.hero.title}           // ✅ New way
{t.common.test.title}    // ✅ New way
```
