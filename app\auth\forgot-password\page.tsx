"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Mail, Shield, Clock, Headphones, Lock } from 'lucide-react'
import { toast } from 'sonner'

import { useAuth } from '@/lib/auth/context'
import { useAuthForm } from '@/lib/auth/hooks'
import { forgotPasswordSchema, type ForgotPasswordFormData } from '@/lib/auth/types'
import SimpleAuthLayout from '@/components/auth/SimpleAuthLayout'
import AuthInput from '@/components/auth/AuthInput'
import AuthButton from '@/components/auth/AuthButton'
import AuthError from '@/components/auth/AuthError'
import AuthSuccess from '@/components/auth/AuthSuccess'
import GuestRoute from '@/components/auth/GuestRoute'

export default function ForgotPasswordPage() {
  const { resetPassword } = useAuth()
  const { isSubmitting, errors, handleSubmit, clearErrors } = useAuthForm()
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors: formErrors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  const onSubmit = async (data: ForgotPasswordFormData) => {
    clearErrors()
    setSuccessMessage(null)
    
    await handleSubmit(
      async () => {
        const result = await resetPassword(data.email)
        return result
      },
      () => {
        setSuccessMessage('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني')
        toast.success('تم إرسال رابط الاستعادة')
      }
    )
  }

  const rightContent = (
    <div className="space-y-8">
      <div>
        <h2 className="text-4xl font-bold mb-4 arabic-heading">
          سنساعدك في استعادة كلمة المرور
        </h2>
        <p className="text-xl text-white/90 arabic-text leading-relaxed">
          أدخل بريدك الإلكتروني وسنرسل لك رابط آمن لإعادة تعيين كلمة المرور
        </p>
      </div>

      <div className="space-y-6">
        <h3 className="text-2xl font-semibold arabic-heading">
          استعادة آمنة وسريعة
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">عملية آمنة ومشفرة</h4>
              <p className="text-white/80 arabic-text">جميع البيانات محمية بأعلى معايير الأمان</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">استعادة سريعة في دقائق</h4>
              <p className="text-white/80 arabic-text">ستصلك رسالة الاستعادة خلال دقائق قليلة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Headphones className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">دعم فني على مدار الساعة</h4>
              <p className="text-white/80 arabic-text">فريق الدعم متاح لمساعدتك في أي وقت</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <Lock className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold arabic-text">حماية كاملة لخصوصيتك</h4>
              <p className="text-white/80 arabic-text">نحن نحترم خصوصيتك ولا نشارك بياناتك</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white/10 rounded-lg p-6">
        <h4 className="font-semibold mb-2 arabic-text">نصائح للأمان:</h4>
        <ul className="space-y-2 text-sm text-white/80 arabic-text">
          <li>• تحقق من صندوق البريد المهمل إذا لم تجد الرسالة</li>
          <li>• الرابط صالح لمدة ساعة واحدة فقط</li>
          <li>• لا تشارك الرابط مع أي شخص آخر</li>
          <li>• اختر كلمة مرور قوية وفريدة</li>
        </ul>
      </div>
    </div>
  )

  return (
    <GuestRoute>
      <SimpleAuthLayout
        title="نسيت كلمة المرور"
        subtitle="سنساعدك في استعادة كلمة المرور"
      >
        <form onSubmit={handleFormSubmit(onSubmit)} className="space-y-6">
          <AuthError error={errors.general} onDismiss={clearErrors} />
          <AuthSuccess message={successMessage} onDismiss={() => setSuccessMessage(null)} />

          <div className="text-center mb-6">
            <p className="text-gray-600 arabic-text">
              أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
            </p>
          </div>

          <AuthInput
            {...register('email')}
            label="البريد الإلكتروني"
            type="email"
            icon={<Mail className="w-5 h-5" />}
            error={formErrors.email?.message}
            placeholder="أدخل بريدك الإلكتروني"
            required
          />

          <AuthButton
            type="submit"
            loading={isSubmitting}
            loadingText="جاري إرسال الرابط..."
          >
            إرسال رابط الاستعادة
          </AuthButton>

          <div className="text-center space-y-4">
            <p className="text-gray-600 arabic-text">
              تذكرت كلمة المرور؟{' '}
              <Link
                href="/auth/login"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                تسجيل الدخول
              </Link>
            </p>
            
            <p className="text-gray-600 arabic-text">
              ليس لديك حساب؟{' '}
              <Link
                href="/auth/register"
                className="text-[#064635] hover:text-[#053d2f] font-medium transition-colors"
              >
                إنشاء حساب جديد
              </Link>
            </p>
          </div>
        </form>
      </SimpleAuthLayout>
    </GuestRoute>
  )
}
