"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface RotatingTextProps {
  words: string[]
  className?: string
  interval?: number
  animationDuration?: number
}

export default function RotatingText({ 
  words, 
  className = "", 
  interval = 2500,
  animationDuration = 300 
}: RotatingTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (words.length <= 1) return

    const timer = setInterval(() => {
      setIsAnimating(true)
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length)
        setIsAnimating(false)
      }, animationDuration / 2)
    }, interval)

    return () => clearInterval(timer)
  }, [words.length, interval, animationDuration])

  if (!words.length) return null

  return (
    <span className={cn("inline-block relative", className)}>
      <span
        className={cn(
          "transition-all duration-300 ease-in-out",
          isAnimating 
            ? "opacity-0 transform -translate-y-2 scale-95" 
            : "opacity-100 transform translate-y-0 scale-100"
        )}
        key={currentIndex}
      >
        {words[currentIndex]}
      </span>
    </span>
  )
}

// Alternative version with slide animation
export function RotatingTextSlide({ 
  words, 
  className = "", 
  interval = 2500,
  animationDuration = 400 
}: RotatingTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isSliding, setIsSliding] = useState(false)

  useEffect(() => {
    if (words.length <= 1) return

    const timer = setInterval(() => {
      setIsSliding(true)
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length)
        setIsSliding(false)
      }, animationDuration / 2)
    }, interval)

    return () => clearInterval(timer)
  }, [words.length, interval, animationDuration])

  if (!words.length) return null

  return (
    <span className={cn("inline-block relative overflow-hidden", className)}>
      <span
        className={cn(
          "block transition-transform duration-400 ease-in-out",
          isSliding 
            ? "transform translate-x-full rtl:-translate-x-full" 
            : "transform translate-x-0"
        )}
        key={currentIndex}
      >
        {words[currentIndex]}
      </span>
    </span>
  )
}

// Version with typewriter effect
export function RotatingTextTypewriter({ 
  words, 
  className = "", 
  interval = 3000,
  typingSpeed = 100,
  deletingSpeed = 50 
}: RotatingTextProps & { typingSpeed?: number; deletingSpeed?: number }) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentText, setCurrentText] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    if (words.length === 0) return

    const currentWord = words[currentIndex]
    
    const timer = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (currentText.length < currentWord.length) {
          setCurrentText(currentWord.slice(0, currentText.length + 1))
        } else {
          // Start deleting after a pause
          setTimeout(() => setIsDeleting(true), interval)
        }
      } else {
        // Deleting
        if (currentText.length > 0) {
          setCurrentText(currentText.slice(0, -1))
        } else {
          setIsDeleting(false)
          setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length)
        }
      }
    }, isDeleting ? deletingSpeed : typingSpeed)

    return () => clearTimeout(timer)
  }, [currentText, currentIndex, isDeleting, words, interval, typingSpeed, deletingSpeed])

  if (!words.length) return null

  return (
    <span className={cn("inline-block", className)}>
      {currentText}
      <span className="animate-pulse">|</span>
    </span>
  )
}
