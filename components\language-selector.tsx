'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Globe } from 'lucide-react'
import { useI18n } from '@/contexts/I18nContext'
import { Locale } from '@/lib/i18n'

const locales: Locale[] = ['ar', 'en', 'fr', 'ru']

const languageNames = {
  ar: 'العربية',
  en: 'English',
  fr: 'Français',
  ru: 'Русский',
}

const languageFlags = {
  ar: '🇸🇦',
  en: '🇺🇸',
  fr: '🇫🇷',
  ru: '🇷🇺',
}

export function LanguageSelector() {
  const { locale, setLocale } = useI18n()

  const switchLocale = (newLocale: Locale) => {
    setLocale(newLocale)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-saudi-primary hover:bg-saudi-50 transition-all duration-200 px-3 py-2 rounded-lg border border-gray-200 hover:border-saudi-primary/20"
        >
          <span className="text-lg">{languageFlags[locale]}</span>
          <span className="hidden sm:inline font-medium">{languageNames[locale]}</span>
          <Globe className="h-4 w-4 opacity-60" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 p-1">
        {locales.map(loc => (
          <DropdownMenuItem
            key={loc}
            onClick={() => switchLocale(loc)}
            className={`cursor-pointer flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 rounded-md transition-colors duration-200 ${
              locale === loc
                ? 'bg-saudi-primary text-white'
                : 'hover:bg-saudi-50 hover:text-saudi-primary'
            }`}
          >
            <span className="text-lg">{languageFlags[loc]}</span>
            <span className="font-medium">{languageNames[loc]}</span>
            {locale === loc && (
              <div className="ml-auto rtl:ml-0 rtl:mr-auto">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
