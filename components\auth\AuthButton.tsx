"use client"

import React from 'react'
import { Loader2 } from 'lucide-react'
import { UnifiedButton } from '@/components/ui/UnifiedButton'
import { cn } from '@/lib/utils'

interface AuthButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean
  loadingText?: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  fullWidth?: boolean
  children: React.ReactNode
}

export default function AuthButton({
  loading = false,
  loadingText,
  variant = 'primary',
  size = 'default',
  fullWidth = true,
  children,
  className,
  disabled,
  ...props
}: AuthButtonProps) {
  return (
    <UnifiedButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      className={cn(
        "h-12 font-medium arabic-text transition-all duration-200",
        fullWidth && "w-full",
        loading && "cursor-not-allowed",
        className
      )}
      {...props}
    >
      {loading && (
        <Loader2 className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 animate-spin" />
      )}
      {loading ? (loadingText || children) : children}
    </UnifiedButton>
  )
}
