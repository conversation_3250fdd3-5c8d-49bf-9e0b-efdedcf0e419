"use client"

import React from 'react'
import { cn } from '@/lib/utils'

interface AuthCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  description?: string
}

export default function AuthCard({ 
  children, 
  className,
  title,
  description 
}: AuthCardProps) {
  return (
    <div className={cn(
      "bg-white rounded-2xl border border-gray-200 p-8 shadow-lg",
      className
    )}>
      {(title || description) && (
        <div className="mb-6 text-center">
          {title && (
            <h2 className="text-2xl font-bold text-gray-900 arabic-heading mb-2">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-gray-600 arabic-text">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  )
}
