'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Search,
  Menu,
  X,
  User,
  Globe,
  ChevronDown,
  ShoppingBag,
  Newspaper,
  HelpCircle,
  Headphones,
  Smartphone,
  Palette,
  Grid3X3,
  LogOut,
  Settings,
  Heart,
  Package,
} from 'lucide-react'
import { SITE_CONFIG } from '@/lib/constants'
import { MAIN_NAVIGATION, AUTH_MENU_ITEMS } from '@/lib/site-config'
import { UnifiedButton } from '@/components/ui/UnifiedButton'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { LanguageSelector } from '@/components/language-selector'
import NestedDropdownMenu from '@/components/ui/NestedDropdownMenu'
import SearchOverlay from './SearchOverlay'
import { useSession } from '@/lib/auth/hooks'

// Icon mapping for navigation items
const iconMap = {
  'shopping-bag': ShoppingBag,
  newspaper: Newspaper,
  'help-circle': HelpCircle,
  headphones: Headphones,
  smartphone: Smartphone,
  palette: Palette,
  'grid-3x3': Grid3X3,
  globe: Globe,
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  // Authentication state
  const { user, profile, isAuthenticated, logout } = useSession()

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const openSearch = () => {
    setIsSearchOpen(true)
  }

  const closeSearch = () => {
    setIsSearchOpen(false)
  }

  return (
    <>
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          {/* Main Header */}
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-saudi-gradient rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">N</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-gray-900">{SITE_CONFIG.name}</span>
                  <span className="text-sm text-saudi-primary arabic-text">
                    {SITE_CONFIG.nameArabic}
                  </span>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
              {MAIN_NAVIGATION.map(item => {
                const IconComponent = iconMap[item.icon as keyof typeof iconMap]

                // Special handling for Templates dropdown with nested submenus
                if (item.name === 'القوالب' && item.hasDropdown && item.dropdownItems) {
                  return (
                    <NestedDropdownMenu
                      key={item.href}
                      triggerText={item.name}
                      triggerIcon={item.icon}
                      items={item.dropdownItems}
                    />
                  )
                }

                // Regular navigation items
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200"
                  >
                    {IconComponent && <IconComponent className="w-4 h-4" />}
                    <span className="arabic-text">{item.name}</span>
                  </Link>
                )
              })}
            </nav>

            {/* Search Button - Desktop */}
            <div className="hidden md:flex items-center">
              <UnifiedButton
                variant="ghost"
                size="sm"
                onClick={openSearch}
                className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200"
              >
                <Search className="w-4 h-4" />
                <span className="arabic-text">بحث</span>
              </UnifiedButton>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {/* Language Switcher */}
              <div className="hidden md:flex">
                <LanguageSelector />
              </div>

              {/* Authentication Section */}
              {isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200 px-3 py-2 rounded-lg">
                      <div className="w-8 h-8 bg-[#064635] rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <span className="arabic-text">{profile?.full_name || user?.email || 'المستخدم'}</span>
                      <ChevronDown className="w-4 h-4" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link
                        href={AUTH_MENU_ITEMS.profile.href}
                        className="flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <User className="w-4 h-4" />
                        <span className="arabic-text">{AUTH_MENU_ITEMS.profile.name}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        href={AUTH_MENU_ITEMS.orders.href}
                        className="flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <Package className="w-4 h-4" />
                        <span className="arabic-text">{AUTH_MENU_ITEMS.orders.name}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link
                        href={AUTH_MENU_ITEMS.favorites.href}
                        className="flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <Heart className="w-4 h-4" />
                        <span className="arabic-text">{AUTH_MENU_ITEMS.favorites.name}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link
                        href={AUTH_MENU_ITEMS.settings.href}
                        className="flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <Settings className="w-4 h-4" />
                        <span className="arabic-text">{AUTH_MENU_ITEMS.settings.name}</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={logout}
                      className="flex items-center space-x-2 rtl:space-x-reverse text-red-600 cursor-pointer"
                    >
                      <LogOut className="w-4 h-4" />
                      <span className="arabic-text">تسجيل الخروج</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-green-600 transition-colors duration-200 px-3 py-2 rounded-lg arabic-text">
                      <User className="w-4 h-4" />
                      <span>تسجيل الدخول</span>
                      <ChevronDown className="w-4 h-4" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40">
                    <DropdownMenuItem asChild>
                      <Link href="/auth/login" className="arabic-text">
                        تسجيل الدخول
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/auth/register" className="arabic-text">
                        إنشاء حساب
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Mobile Menu Button */}
              <UnifiedButton variant="ghost" size="sm" className="lg:hidden" onClick={toggleMenu}>
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </UnifiedButton>
            </div>
          </div>

          {/* Mobile Search Bar */}
          <div className="md:hidden pb-4">
            <UnifiedButton
              variant="ghost"
              onClick={openSearch}
              className="w-full flex items-center justify-center space-x-2 rtl:space-x-reverse py-3 text-gray-700 hover:text-green-600"
            >
              <Search className="w-4 h-4" />
              <span className="arabic-text">البحث في القوالب والمقالات</span>
            </UnifiedButton>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200">
            <div className="container mx-auto px-4 py-4">
              {/* Navigation Links */}
              <nav className="space-y-4 mb-6">
                {MAIN_NAVIGATION.map(item => {
                  const IconComponent = iconMap[item.icon as keyof typeof iconMap]

                  return (
                    <div key={item.href}>
                      <Link
                        href={item.href}
                        className="flex items-center space-x-3 rtl:space-x-reverse text-base font-medium text-gray-700 hover:text-green-600 transition-colors duration-200 arabic-text py-2"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {IconComponent && <IconComponent className="w-5 h-5" />}
                        <span>{item.name}</span>
                      </Link>

                      {/* Mobile Dropdown Items - Special handling for Templates */}
                      {item.hasDropdown && item.dropdownItems && (
                        <div className="mr-8 rtl:mr-0 rtl:ml-8 mt-2 space-y-2">
                          {item.dropdownItems.map(dropdownItem => {
                            const DropdownIcon = iconMap[dropdownItem.icon as keyof typeof iconMap]
                            return (
                              <div key={dropdownItem.href}>
                                <Link
                                  href={dropdownItem.href}
                                  className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 hover:text-green-600 transition-colors duration-200 arabic-text py-1"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  {DropdownIcon && <DropdownIcon className="w-4 h-4" />}
                                  <span>{dropdownItem.name}</span>
                                </Link>

                                {/* Sub-items for mobile */}
                                {dropdownItem.hasSubItems && dropdownItem.subItems && (
                                  <div className="mr-6 rtl:mr-0 rtl:ml-6 mt-1 space-y-1">
                                    {dropdownItem.subItems.map(subItem => {
                                      const SubIcon = iconMap[subItem.icon as keyof typeof iconMap]
                                      return (
                                        <Link
                                          key={subItem.href}
                                          href={subItem.href}
                                          className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-gray-500 hover:text-green-600 transition-colors duration-200 arabic-text py-1"
                                          onClick={() => setIsMenuOpen(false)}
                                        >
                                          {SubIcon && <SubIcon className="w-3 h-3" />}
                                          <span>{subItem.name}</span>
                                        </Link>
                                      )
                                    })}
                                  </div>
                                )}
                              </div>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )
                })}
              </nav>

              {/* Mobile Actions */}
              <div className="space-y-3 pt-4 border-t border-gray-200">
                <div className="flex justify-start">
                  <LanguageSelector />
                </div>

                {isAuthenticated ? (
                  <>
                    <UnifiedButton variant="ghost" className="w-full justify-start arabic-text">
                      <User className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                      {profile?.full_name || user?.email || 'المستخدم'}
                    </UnifiedButton>
                    <UnifiedButton
                      variant="ghost"
                      onClick={logout}
                      className="w-full justify-start arabic-text text-red-600"
                    >
                      <LogOut className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                      تسجيل الخروج
                    </UnifiedButton>
                  </>
                ) : (
                  <>
                    <UnifiedButton asChild variant="ghost" className="w-full justify-start arabic-text">
                      <Link href="/auth/login">
                        <User className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                        تسجيل الدخول
                      </Link>
                    </UnifiedButton>
                    <UnifiedButton asChild variant="primary" className="w-full arabic-text">
                      <Link href="/auth/register">
                        إنشاء حساب
                      </Link>
                    </UnifiedButton>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Search Overlay */}
      <SearchOverlay
        isOpen={isSearchOpen}
        onClose={closeSearch}
        onSearch={query => {
          console.log('Search query:', query)
          // Handle search logic here
        }}
      />
    </>
  )
}
