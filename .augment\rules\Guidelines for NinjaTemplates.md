---
type: "manual"
---

For each page, section, or component on the site, a separate translation file must be created for each language within the locales/ar and locales/en folders. Using a single, generic translation file is not permitted.

The names of the translation files must match the name of the page or section they represent. For example, the home page uses home.json, the shopping cart uses cart.json, and so on.

No direct text is allowed within the code. All text must be extracted from the translation files.

The use of direct colors such as hexadecimal codes (e.g., #ffffff), rgb, rgba, or hsl is not permitted within any component or page.

Colors must be used exclusively from the styles/globals.css file using CSS variables defined in :root.

When creating any new component (either manually or using generation tools like AugmentCode), you must do the following:

Add a separate translation file for it in both languages.

Use colors from globals.css.

Do not duplicate existing translation keys in other files.

Arabic and English are always supported.

No predefined fonts, sizes, or colors may be used in the overall design of the site.

All components must be RTL and LTR compliant by default.

Any automated generation process must adhere to these rules in full, without exception.

Messages, headings, buttons, paragraphs, tooltips, or values ​​must not be written in JSX or directly within the code. All must be translated and pulled from the translation files according to the active language.

When editing or updating a component, the translation files associated with it must be updated, and no unused text must be left.

The same label or key must not be repeated in more than one translation file unless it is for shared content.