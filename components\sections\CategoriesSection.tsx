"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Smartphone,
  Palette,
  Globe,
  Grid3X3,
  ArrowLeft,
  TrendingUp,
  Code,
  Layers,
  Monitor,
  Sparkles,
  Star,
  Users,
  Download
} from 'lucide-react'
import { TEMPLATE_CATEGORIES } from '@/lib/constants'

// Enhanced icon mapping with more variety
const iconMap = {
  smartphone: Smartphone,
  palette: Palette,
  globe: Globe,
  grid: Grid3X3,
  code: Code,
  layers: Layers,
  monitor: Monitor,
}

// Enhanced categories data with Saudi color scheme
const enhancedCategories = [
  {
    id: 'web-apps',
    name: 'تطبيقات الويب',
    description: 'قوالب تطبيقات ويب حديثة ومتجاوبة',
    icon: 'globe',
    count: 120,
    rating: 4.9,
    color: 'from-saudi-primary to-saudi-secondary',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: 'الأكثر طلباً',
    badgeColor: 'bg-saudi-600',
    href: '/templates?category=web-apps'
  },
  {
    id: 'mobile-apps',
    name: 'تطبيقات الجوال',
    description: 'واجهات تطبيقات iOS و Android',
    icon: 'smartphone',
    count: 85,
    rating: 4.8,
    color: 'from-saudi-secondary to-saudi-600',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: 'جديد',
    badgeColor: 'bg-saudi-500',
    href: '/templates?category=mobile-apps'
  },
  {
    id: 'ui-kits',
    name: 'مجموعات واجهات',
    description: 'مكونات UI جاهزة للاستخدام',
    icon: 'layers',
    count: 95,
    rating: 4.7,
    color: 'from-saudi-600 to-saudi-700',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: 'مميز',
    badgeColor: 'bg-saudi-primary',
    href: '/templates?category=ui-kits'
  },
  {
    id: 'dashboards',
    name: 'لوحات التحكم',
    description: 'لوحات إدارية احترافية',
    icon: 'monitor',
    count: 60,
    rating: 4.9,
    color: 'from-saudi-500 to-saudi-primary',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: 'مجاني',
    badgeColor: 'bg-saudi-secondary',
    href: '/templates?category=dashboards'
  },
  {
    id: 'landing-pages',
    name: 'صفحات الهبوط',
    description: 'صفحات تسويقية عالية التحويل',
    icon: 'code',
    count: 75,
    rating: 4.6,
    color: 'from-saudi-400 to-saudi-500',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: null,
    badgeColor: '',
    href: '/templates?category=landing-pages'
  },
  {
    id: 'e-commerce',
    name: 'التجارة الإلكترونية',
    description: 'متاجر إلكترونية متكاملة',
    icon: 'grid',
    count: 45,
    rating: 4.8,
    color: 'from-saudi-700 to-saudi-800',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: 'الأعلى تقييماً',
    badgeColor: 'bg-saudi-600',
    href: '/templates?category=e-commerce'
  },
  {
    id: 'portfolios',
    name: 'معارض الأعمال',
    description: 'قوالب عرض الأعمال الإبداعية',
    icon: 'palette',
    count: 55,
    rating: 4.7,
    color: 'from-saudi-300 to-saudi-400',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: null,
    badgeColor: '',
    href: '/templates?category=portfolios'
  },
  {
    id: 'blogs',
    name: 'المدونات',
    description: 'قوالب مدونات أنيقة وسريعة',
    icon: 'layers',
    count: 40,
    rating: 4.5,
    color: 'from-saudi-secondary to-saudi-primary',
    bgColor: 'from-saudi-50 to-saudi-100',
    badge: null,
    badgeColor: '',
    href: '/templates?category=blogs'
  }
]

export default function CategoriesSection() {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)

  return (
    <section id="categories" className="py-24 bg-gradient-to-br from-white via-gray-50/50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-[#064635] rounded-full blur-3xl transform translate-x-1/3"></div>
        <div className="absolute bottom-1/4 left-0 w-72 h-72 bg-[#064635] rounded-full blur-3xl transform -translate-x-1/3"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-[#064635]/10 text-[#064635] px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
            <span className="arabic-text">تصنيفات متنوعة</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 arabic-heading leading-tight">
            فئات القوالب
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic-text leading-relaxed">
            اختر من بين مجموعة متنوعة من الفئات المتخصصة لتجد القالب المثالي لمشروعك التقني
          </p>

          {/* Stats Bar */}
          <div className="flex items-center justify-center space-x-8 rtl:space-x-reverse mt-8">
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
              <Grid3X3 className="w-5 h-5 text-[#064635]" />
              <span className="font-semibold arabic-text">8 فئات رئيسية</span>
            </div>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
              <Download className="w-5 h-5 text-[#064635]" />
              <span className="font-semibold arabic-text">500+ قالب</span>
            </div>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
              <Users className="w-5 h-5 text-[#064635]" />
              <span className="font-semibold arabic-text">10K+ مطور</span>
            </div>
          </div>
        </div>

        {/* Enhanced 4-Column Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {enhancedCategories.map((category, index) => {
            const IconComponent = iconMap[category.icon as keyof typeof iconMap]
            const isHovered = hoveredCategory === category.id

            return (
              <Link
                key={category.id}
                href={category.href}
                className="group block"
                onMouseEnter={() => setHoveredCategory(category.id)}
                onMouseLeave={() => setHoveredCategory(null)}
              >
                <div className={`relative bg-white rounded-3xl border border-gray-100 p-6 text-center hover:shadow-2xl hover:shadow-[#064635]/10 hover:border-[#064635]/20 transition-all duration-500 transform hover:-translate-y-3 hover:scale-[1.02] overflow-hidden ${
                  isHovered ? 'ring-2 ring-[#064635]/20' : ''
                }`}>
                  {/* Background Gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${category.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>

                  {/* Badge */}
                  {category.badge && (
                    <div className={`absolute top-4 right-4 rtl:right-auto rtl:left-4 ${category.badgeColor} text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg transform -translate-y-1 group-hover:translate-y-0 transition-transform duration-300`}>
                      <span className="arabic-text">{category.badge}</span>
                    </div>
                  )}

                  {/* Enhanced Icon */}
                  <div className="relative z-10">
                    <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${category.color} flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg group-hover:shadow-xl`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>

                    {/* Category Name */}
                    <h3 className="text-lg font-bold text-gray-900 mb-3 arabic-heading group-hover:text-[#064635] transition-colors duration-300 leading-tight">
                      {category.name}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 mb-4 arabic-text leading-relaxed text-sm">
                      {category.description}
                    </p>

                    {/* Enhanced Stats */}
                    <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <TrendingUp className="w-3 h-3 ml-1 rtl:ml-0 rtl:mr-1" />
                        <span className="arabic-text font-medium">
                          {category.count}+ قالب
                        </span>
                      </div>
                      <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        <span className="font-medium">{category.rating}</span>
                      </div>
                    </div>

                    {/* Enhanced CTA */}
                    <div className="flex items-center justify-center">
                      <div className={`flex items-center justify-center bg-gradient-to-r ${category.color} text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 group-hover:shadow-lg transform group-hover:-translate-y-1 min-w-[120px]`}>
                        <span className="arabic-text">استكشف</span>
                        <ArrowLeft className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 rtl:rotate-180 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>

                    {/* Hover Effect Indicator */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#064635] to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>

        {/* Enhanced Bottom CTA Section */}
        <div className="mt-20">
          <div className="bg-gradient-to-r from-[#064635] to-[#053d2f] rounded-3xl p-8 lg:p-12 text-center relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full blur-3xl transform translate-x-1/3 -translate-y-1/3"></div>
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full blur-3xl transform -translate-x-1/3 translate-y-1/3"></div>
            </div>

            <div className="relative z-10 max-w-4xl mx-auto">
              <div className="inline-flex items-center bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                <span className="arabic-text">خدمة مميزة</span>
              </div>

              <h3 className="text-3xl lg:text-4xl font-bold text-white mb-6 arabic-heading leading-tight">
                لا تجد ما تبحث عنه؟
              </h3>

              <p className="text-xl text-white/90 mb-8 arabic-text leading-relaxed max-w-2xl mx-auto">
                فريقنا المتخصص جاهز لتصميم قالب مخصص يلبي احتياجاتك بدقة
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
                <Link href="/contact">
                  <button className="bg-white text-[#064635] hover:bg-gray-50 px-8 py-3 rounded-xl font-bold arabic-text transition-all duration-300 hover:shadow-lg hover:-translate-y-1 min-w-[200px]">
                    اطلب قالب مخصص
                  </button>
                </Link>

                <Link href="/templates">
                  <button className="border-2 border-white text-white hover:bg-white hover:text-[#064635] px-8 py-3 rounded-xl font-bold arabic-text transition-all duration-300 hover:shadow-lg hover:-translate-y-1 min-w-[200px]">
                    تصفح جميع القوالب
                  </button>
                </Link>
              </div>

              {/* Features */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-white/90">
                <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Code className="w-5 h-5" />
                  </div>
                  <span className="arabic-text font-medium">كود نظيف ومحسن</span>
                </div>

                <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5" />
                  </div>
                  <span className="arabic-text font-medium">دعم فني مجاني</span>
                </div>

                <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Star className="w-5 h-5" />
                  </div>
                  <span className="arabic-text font-medium">ضمان الجودة</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
