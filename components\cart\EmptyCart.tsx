"use client"

import React from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ShoppingBag, Sparkles } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useI18n } from '@/contexts/I18nContext'
import cartTranslations from '@/lib/translations/cart.json'

export default function EmptyCart() {
  const { locale } = useI18n()
  const t = cartTranslations[locale as keyof typeof cartTranslations]

  return (
    <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
      {/* Animated Illustration */}
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="relative mb-6"
      >
        <div className="w-24 h-24 bg-saudi-50 rounded-full flex items-center justify-center relative">
          <ShoppingBag className="w-12 h-12 text-saudi-primary/60" />
          
          {/* Floating sparkles */}
          <motion.div
            animate={{ 
              y: [0, -8, 0],
              rotate: [0, 10, 0]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute -top-2 -right-2"
          >
            <Sparkles className="w-4 h-4 text-saudi-primary/40" />
          </motion.div>
          
          <motion.div
            animate={{ 
              y: [0, -6, 0],
              rotate: [0, -8, 0]
            }}
            transition={{ 
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
            className="absolute -bottom-1 -left-2"
          >
            <Sparkles className="w-3 h-3 text-saudi-primary/30" />
          </motion.div>
        </div>
      </motion.div>

      {/* Empty State Text */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mb-8"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-2 arabic-heading">
          {t.empty}
        </h3>
        <p className="text-gray-600 arabic-text max-w-sm">
          {t.emptyDescription}
        </p>
      </motion.div>

      {/* Browse Templates Button */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Button asChild className="bg-saudi-primary hover:bg-saudi-secondary text-white px-6 py-3">
          <Link href="/templates" className="arabic-text">
            {t.browseTemplates}
          </Link>
        </Button>
      </motion.div>
    </div>
  )
}
